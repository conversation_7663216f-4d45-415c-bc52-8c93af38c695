// #ifdef H5
/**
 * clipboard.min.js  Add support for ES6 module import
 * <AUTHOR>
 * https://www.thorui.cn/doc
 **/
function select(t){var e;if("SELECT"===t.nodeName)t.focus(),e=t.value;else if("INPUT"===t.nodeName||"TEXTAREA"===t.nodeName){var i=t.hasAttribute("readonly");i||t.setAttribute("readonly",""),t.select(),t.setSelectionRange(0,t.value.length),i||t.removeAttribute("readonly"),e=t.value}else{t.hasAttribute("contenteditable")&&t.focus();var n=window.getSelection(),r=document.createRange();r.selectNodeContents(t),n.removeAllRanges(),n.addRange(r),e=n.toString()}return e}class ClipboardAction{constructor(t){this.resolveOptions(t),this.initSelection()}resolveOptions(t={}){this.action=t.action,this.container=t.container,this.emitter=t.emitter,this.target=t.target,this.text=t.text,this.trigger=t.trigger,this.selectedText=""}initSelection(){this.text?this.selectFake():this.target&&this.selectTarget()}selectFake(){const t="rtl"==document.documentElement.getAttribute("dir");this.removeFake(),this.fakeHandlerCallback=(()=>this.removeFake()),this.fakeHandler=this.container.addEventListener("click",this.fakeHandlerCallback)||!0,this.fakeElem=document.createElement("textarea"),this.fakeElem.style.fontSize="12pt",this.fakeElem.style.border="0",this.fakeElem.style.padding="0",this.fakeElem.style.margin="0",this.fakeElem.style.position="absolute",this.fakeElem.style[t?"right":"left"]="-9999px";let e=window.pageYOffset||document.documentElement.scrollTop;this.fakeElem.style.top=`${e}px`,this.fakeElem.setAttribute("readonly",""),this.fakeElem.value=this.text,this.container.appendChild(this.fakeElem),this.selectedText=select(this.fakeElem),this.copyText()}removeFake(){this.fakeHandler&&(this.container.removeEventListener("click",this.fakeHandlerCallback),this.fakeHandler=null,this.fakeHandlerCallback=null),this.fakeElem&&(this.container.removeChild(this.fakeElem),this.fakeElem=null)}selectTarget(){this.selectedText=select(this.target),this.copyText()}copyText(){let t;try{t=document.execCommand(this.action)}catch(e){t=!1}this.handleResult(t)}handleResult(t){this.emitter.emit(t?"success":"error",{action:this.action,text:this.selectedText,trigger:this.trigger,clearSelection:this.clearSelection.bind(this)})}clearSelection(){this.trigger&&this.trigger.focus(),window.getSelection().removeAllRanges()}set action(t="copy"){if(this._action=t,"copy"!==this._action&&"cut"!==this._action)throw new Error('Invalid "action" value, use either "copy" or "cut"')}get action(){return this._action}set target(t){if(void 0!==t){if(!t||"object"!=typeof t||1!==t.nodeType)throw new Error('Invalid "target" value, use a valid Element');if("copy"===this.action&&t.hasAttribute("disabled"))throw new Error('Invalid "target" attribute. Please use "readonly" instead of "disabled" attribute');if("cut"===this.action&&(t.hasAttribute("readonly")||t.hasAttribute("disabled")))throw new Error('Invalid "target" attribute. You can\'t cut text from elements with "readonly" or "disabled" attributes');this._target=t}}get target(){return this._target}destroy(){this.removeFake()}}function Emitter(){}Emitter.prototype={on:function(t,e,i){var n=this.e||(this.e={});return(n[t]||(n[t]=[])).push({fn:e,ctx:i}),this},once:function(t,e,i){var n=this;function r(){n.off(t,r),e.apply(i,arguments)}return r._=e,this.on(t,r,i)},emit:function(t){for(var e=[].slice.call(arguments,1),i=((this.e||(this.e={}))[t]||[]).slice(),n=0,r=i.length;n<r;n++)i[n].fn.apply(i[n].ctx,e);return this},off:function(t,e){var i=this.e||(this.e={}),n=i[t],r=[];if(n&&e)for(var o=0,s=n.length;o<s;o++)n[o].fn!==e&&n[o].fn._!==e&&r.push(n[o]);return r.length?i[t]=r:delete i[t],this}};let is={node:function(t){return void 0!==t&&t instanceof HTMLElement&&1===t.nodeType},nodeList:function(t){var e=Object.prototype.toString.call(t);return void 0!==t&&("[object NodeList]"===e||"[object HTMLCollection]"===e)&&"length"in t&&(0===t.length||exports.node(t[0]))},string:function(t){return"string"==typeof t||t instanceof String},fn:function(t){return"[object Function]"===Object.prototype.toString.call(t)}};var DOCUMENT_NODE_TYPE=9;if("undefined"!=typeof Element&&!Element.prototype.matches){var proto=Element.prototype;proto.matches=proto.matchesSelector||proto.mozMatchesSelector||proto.msMatchesSelector||proto.oMatchesSelector||proto.webkitMatchesSelector}function closest(t,e){for(;t&&t.nodeType!==DOCUMENT_NODE_TYPE;){if("function"==typeof t.matches&&t.matches(e))return t;t=t.parentNode}}function _delegate(t,e,i,n,r){var o=listener.apply(this,arguments);return t.addEventListener(i,o,r),{destroy:function(){t.removeEventListener(i,o,r)}}}function delegate(t,e,i,n,r){return"function"==typeof t.addEventListener?_delegate.apply(null,arguments):"function"==typeof i?_delegate.bind(null,document).apply(null,arguments):("string"==typeof t&&(t=document.querySelectorAll(t)),Array.prototype.map.call(t,function(t){return _delegate(t,e,i,n,r)}))}function listener(t,e,i,n){return function(i){i.delegateTarget=closest(i.target,e),i.delegateTarget&&n.call(t,i)}}function listen(t,e,i){if(!t&&!e&&!i)throw new Error("Missing required arguments");if(!is.string(e))throw new TypeError("Second argument must be a String");if(!is.fn(i))throw new TypeError("Third argument must be a Function");if(is.node(t))return listenNode(t,e,i);if(is.nodeList(t))return listenNodeList(t,e,i);if(is.string(t))return listenSelector(t,e,i);throw new TypeError("First argument must be a String, HTMLElement, HTMLCollection, or NodeList")}function listenNode(t,e,i){return t.addEventListener(e,i),{destroy:function(){t.removeEventListener(e,i)}}}function listenNodeList(t,e,i){return Array.prototype.forEach.call(t,function(t){t.addEventListener(e,i)}),{destroy:function(){Array.prototype.forEach.call(t,function(t){t.removeEventListener(e,i)})}}}function listenSelector(t,e,i){return delegate(document.body,t,e,i)}class Clipboard extends Emitter{constructor(t,e){super(),this.resolveOptions(e),this.listenClick(t)}resolveOptions(t={}){this.action="function"==typeof t.action?t.action:this.defaultAction,this.target="function"==typeof t.target?t.target:this.defaultTarget,this.text="function"==typeof t.text?t.text:this.defaultText,this.container="object"==typeof t.container?t.container:document.body}listenClick(t){this.listener=listen(t,"click",t=>this.onClick(t))}onClick(t){const e=t.delegateTarget||t.currentTarget;this.clipboardAction&&(this.clipboardAction=null),this.clipboardAction=new ClipboardAction({action:this.action(e),target:this.target(e),text:this.text(e),container:this.container,trigger:e,emitter:this})}defaultAction(t){return getAttributeValue("action",t)}defaultTarget(t){const e=getAttributeValue("target",t);if(e)return document.querySelector(e)}static isSupported(t=["copy","cut"]){const e="string"==typeof t?[t]:t;let i=!!document.queryCommandSupported;return e.forEach(t=>{i=i&&!!document.queryCommandSupported(t)}),i}defaultText(t){return getAttributeValue("text",t)}destroy(){this.listener.destroy(),this.clipboardAction&&(this.clipboardAction.destroy(),this.clipboardAction=null)}}function getAttributeValue(t,e){const i=`data-clipboard-${t}`;if(e.hasAttribute(i))return e.getAttribute(i)}export default Clipboard;
// #endif