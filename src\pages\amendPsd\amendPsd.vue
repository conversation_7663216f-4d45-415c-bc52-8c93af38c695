<template>
	<view class="padd-t-5" style="background-color:#F3F7FA;height: 100vh;">
		<tui-form :tip-top="0" ref="form" :show-message="false" :model="formData">
			<tui-form-item label="旧密码" prop="oldPassword" asterisk>
				<tui-input type="password" v-model="formData.oldPassword" padding="0" :borderBottom="false" maxlength="20" placeholder="请输入旧密码"></tui-input>
			</tui-form-item>
			<tui-form-item label="新密码" prop="newPassword" asterisk>
				<tui-input type="password" v-model="formData.newPassword" padding="0" :borderBottom="false" maxlength="20" placeholder="请输入新密码"></tui-input>
			</tui-form-item>
			<tui-form-item label="确认密码" prop="passwordConfirm" asterisk>
				<tui-input type="password" v-model="formData.passwordConfirm" padding="0" :borderBottom="false" maxlength="20" placeholder="请确认密码"></tui-input>
			</tui-form-item>

			<view class="flex flex-center marg-t-20">
				<tui-button shape="circle" width="400rpx" height="84rpx" bold @click="submit">提交</tui-button>
			</view>
		</tui-form>
	</view>
</template>

<script>
	import MD5 from 'crypto-js/md5'
	export default {
		data() {
			return {
				formData:{
					oldPassword:'',
					newPassword:'',
					passwordConfirm:'',
				},
				rules: [
					{name: "oldPassword",rule: ["required"],msg: ["原密码不能为空"]},
					{name: "newPassword",rule: ["required"],msg: ["新密码不能为空"],
						validator: [{msg: "8~16位大小写字母+数字+特殊字符(!@$%&*.?)",method: (value) => {
							const pattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*.?&])[A-Za-z\d@$!%*.?&]{8,16}$/;
							return pattern.test(value);
						}}]
					},
					{name: "passwordConfirm",rule: ["required"],msg: ["确认密码不能为空"],
						validator: [{msg: "8~16位大小写字母+数字+特殊字符(!@$%&*.?)",method: (value) => {
							const pattern = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*.?&])[A-Za-z\d@$!%*.?&]{8,16}$/;
							return pattern.test(value);
						}}]
					},
				],
			}
		},
		onReady() {
			// this.$refs.uForm.setRules(this.rules);//小程序兼容
		},
		methods: {
			//提交
			submit() {
				this.$refs.form.validate(this.formData, this.rules, true).then(res => {
					if (res.isPass) {
						console.log(this.formData)
						if(this.formData.oldPassword==this.formData.newPassword){
							uni.showToast({
								mask: true,
								icon:'none',
								title:'原密码和新密码一样!'
							})
							return
						}
						if(this.formData.newPassword!==this.formData.passwordConfirm){
							uni.showToast({
								mask: true,
								icon:'none',
								title:'两次新密码不一致'
							})
							return
						}
						this.http.request({
							url:this.$apiUrl.updatePassword,
							method:'PUT',
							data:{
								oldPassword:this.formData.oldPassword.replace(/\ +/g, "").toString(),
								newPassword:this.formData.newPassword.replace(/\ +/g, "").toString(),
								// rawPassword:this.formData.passwordConfirm.replace(/\ +/g, "").toString(),
							}
						}).then(res => {
							console.log('修改密码成功:',res);
							this.tui.toast("修改成功")
							// uni.removeStorageSync('password');
							// uni.removeStorageSync('isCheckedInfor');//是否记住密码
							this.tui.Fun_loginOut();//退出登录
							setTimeout(()=>{
								uni.reLaunch({
									url:'/pages/login/index',
								})
							},1500)
						})
					} else {
						console.log('校验不通过:',res)
					}
				}).catch(errors => {
					console.log('校验失败:',errors)
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
    // page{background-color: #fff;}
</style>