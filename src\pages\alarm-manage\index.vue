<template>
	<view class="container height-all flex flex-col">
		<tui-tab :tabs="tabs" :current="tabsKey" isFixed @change="fun_tabsChange" class="border-b-ddd"></tui-tab>
		<tui-white-space height="80"></tui-white-space>
		
		<!-- 列表菜单 -->
		<view class="font-14 flex-item-grow-1" style="min-height:0;height: 100%;">
			<mescroll-uni :fixed="false" ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
				<view class="bg-color-fff marg-t-10 padd-0_20" v-for="(item,key) of dataList" :key="key">
					<view class="padd-10_0">
						<view class=" border-b-ddd padd-b-5">报警时间：{{item.warningTime}}</view>
						<view class="padd-5_0" @click="fun_goInfo(item.heatWarningRecordId)">
							<view class="flex flex-both">
								<view class="flex">
									<text class="flex-item-shrink-0">报警等级：</text>
									<text>{{alarmLevelOptions.find((v) => v.value == item.warningLevel)?.text}}</text>
								</view>
								<!-- <view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="riskTagType[item.status]" shape="circleLeft" padding="6rpx 12rpx">{{bjztOption.find((v) => v.value == item.status)?.text}}</tui-tag>
								</view> -->
							</view>
							<view>归属名称：{{item.relationName}}</view>
							<view>点位名称：{{item.heatPositionName}}</view>
							<view>报警内容：{{item.warningContent}}</view>
							<!-- <view>报警值：{{item.warningValue}}</view> -->
							<!-- <view>报警点位置：{{'??'}}</view> -->
						</view>
						<!-- <view class=" border-t-ddd padd-t-5 text-right">
							<tui-tag style="margin-left: 20rpx;" type="warning" plain v-if="!item.bjczjddm||item.bjczjddm == '2'" @click="fun_goPage(item,'maint')">派发维修</tui-tag>
							<tui-tag style="margin-left: 20rpx;" type="green" plain v-else-if="item.bjczjddm == '5'" @click="fun_goPage(item,'end')">办结</tui-tag>
							<tui-tag style="margin-left: 20rpx;" type="primary" plain v-if="item.confirmFlag == '0'" @click="fun_confirm(item.heatWarningRecordId)">确认</tui-tag>
						</view> -->
					</view>
				</view>
			</mescroll-uni>
		</view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';

	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin],
		data() {
			return {
				tabsKey: 0,
				tabsId:'',
				tabs: [
					{name: "报警中",id: '0'},
					{name: "已恢复",id: '1'},
					{name: "已确认",id: '99'},
				],
				dataList: [
					// {maintType:1,startTime:'2025-03-05 10:10:10',person:'人员名称',type:'类型',level:'3',desc:'描述'},
					// {maintType:2,startTime:'2025-03-05 10:10:10',level:'4',device:'设备',position:'位置',itemId:'5',alarmId:'6'},
				],
				upOption:{
					auto: false, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true(下拉加载的配置中的自动执行也默认true)
					// page: {
					//     num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
					//     size: 10, // 每页数据的数量
					//     time: null // 加载第一页数据服务器返回的时间; 防止用户翻页时,后台新增了数据从而导致下一页数据重复;
					// },
					toTop:{
						src:''
					},
					textNoMore: '-- 没有更多了 --', // 没有更多数据的提示文本
					empty: {
						// icon: "", // 图标路径
						tip: '暂无相关数据'
					}
				},
				downOption:{
					auto: false, // 是否在初始化完毕之后自动执行下拉刷新的回调; 默认true
				},

				alarmLevelOptions:[],//报警等级
				bjztOption:[],//报警状态
				maintTagType: {
					0: 'warning',
					1: 'warning',
					2: 'primary',
					3: 'danger',
					4: 'green',
				},
				riskTagType: {
					1: 'light-brownish',
					2: 'light-brownish',
					3: 'light-brownish',
					4: 'light-orange',
					5: 'light-blue',
					6: 'light-green',
					7: 'light-danger',
					8: 'light-danger',
					9: 'light-green',
				},
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.tabsKey) {
				this.tabsKey = e.tabsKey;
				this.tabsId = this.tabs[e.tabsKey]?.id;
			}
			this.fun_getDicts();//获取字典
		},
		onShow() {
			this.$nextTick(() => {
				this.dataList = []; // 先置空列表,显示加载进度
				this.mescroll.resetUpScroll(); // 再刷新列表数据(默认会重置page.num=1)
			});
		},
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取字典
			fun_getDicts() {
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHDJ"}
				// }).then( ({data}) => {
				// 	console.log('报警等级:',data)
				// 	this.alarmLevelOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getBusinessDict+'BJZT').then( ({data}) => {
				// 	console.log('报警状态:',data)
				// 	this.bjztOption = data;
				// })
				this.alarmLevelOptions = this.$apiUrl.localCommonDict.alarmLevelOptions;
				this.dangerLevelOptions = this.$apiUrl.localCommonDict.dangerLevelOptions;
			},
			fun_tabsChange(v) {
				console.log('fun_tabsChange',v.index,v.item.id);
				this.tabsKey = v.index;
				this.tabsId = v.item.id;
				this.dataList = [];
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				setTimeout(()=>{
				console.log('upCallback')
				let params = {};
				if(this.tabsKey!=2) {  //非已确认传restoreFlag
					params.restoreFlag = this.tabsId;
				}else{                 //已确认传confirmFlag
					params.confirmFlag = 1;
				}
				//联网加载数据
				// apiGoods(page.num, page.size).then( ({data})=>{
				this.http.post(this.$apiUrl.alarmList, { data:{pageNum:page.num, pageSize:page.size,...params} }).then( ({object}) => {
					//方法二(推荐): 后台接口有返回列表的总数据量 totalSize
					this.mescroll.endBySize(object.records.length, object.total); //必传参数(当前页的数据个数, 总数据量)
					//方法四 (不推荐),会存在一个小问题:比如列表共有20条数据,每页加载10条,共2页.如果只根据当前页的数据个数判断,则需翻到第三页才会知道无更多数据
					// this.mescroll.endSuccess(object.records.length);

					//设置列表数据
					if(page.num == 1) this.dataList = []; //如果是第一页需手动制空列表
					this.dataList=this.dataList.concat(object.records); //追加新数据
				}).catch((err)=>{
					console.log('upCallback_err',err)
					//联网失败, 结束加载
					this.mescroll.endErr();
				})
				},300)
			},
			fun_goInfo(id) {
				uni.navigateTo({
					url: `/pages/alarm-manage/look?gid=${id}`
				});
			},
			fun_goPage(item,type) {
				uni.navigateTo({
					url: `/pages/event-handle/event-handle?gid=${item.heatWarningRecordId}&type=${type}&deviceId=${item.heatPositionId}&deviceName=${item.heatPositionName}&from=alarm`
				});
			},
			//确认
			fun_confirm(id) {
				this.tui.modal('确认', '是否确认？', true, (res) => {
					if (res) {
						this.http.get(this.$apiUrl.alarmConfirm, { data:{keyIds:id.toString(),action:'确认'} }).then( (res) => {
							this.tui.toast('确认成功');
							setTimeout(()=>{
								this.dataList = [];
								this.mescroll.resetUpScroll();
							},1500)
						})
					}
				})
			}
		},
	};
</script>

<style lang="scss">
	// page{background-color: #fff;}
	uni-page-body{height: 100%;}
</style>