var slideBarWidth = 200;
var slideBlockWidth = 32;
var errorRange = 2
var disabled = false

function bool(str) {
	return str === 'true' || str == true ? true : false
}

function isPC() {
	if (typeof navigator !== 'object') return false;
	var userAgentInfo = navigator.userAgent;
	var Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"];
	var flag = true;
	for (var v = 0; v < Agents.length - 1; v++) {
		if (userAgentInfo.indexOf(Agents[v]) > 0) {
			flag = false;
			break;
		}
	}
	return flag;
}
var isH5 = false
if (typeof window === 'object') isH5 = true


function touchstart(e, ins) {
	var state = e.instance.getState()
	var touch = {}
	if (isH5 && isPC()) {
		touch = e;
	} else {
		touch = e.touches[0] || e.changedTouches[0]
	}
	var dataset = e.instance.getDataset()
	state.startX = touch.pageX
	slideBarWidth = +dataset.slidebarwidth
	slideBlockWidth = +dataset.slideblockwidth
	errorRange = +dataset.errorrange
	disabled = bool(dataset.disabled)
}

function styleChange(left, ins) {
	if (!ins) return;
	var tsb = ins.selectComponent('.tui-slider-block')
	var tsg = ins.selectComponent('.tui-slide-glided')
	if (!tsb || !tsg) return;
	if (left == 0) {
		tsb.addClass('tui-slider__reset')
		tsg.addClass('tui-slider__reset')
	} else {
		tsb.removeClass('tui-slider__reset')
		tsg.removeClass('tui-slider__reset')
	}
	tsb.setStyle({
		transform: 'translate3d(' + left + 'px,0,0)'
	})
	tsg.setStyle({
		width: left + 'px'
	})
}

function touchmove(e, ins, event) {
	if (disabled) return;
	var state = {}
	var touch = {}
	if (isH5 && isPC()) {
		touch = e;
		state = event.instance.getState()
	} else {
		state = e.instance.getState()
		touch = e.touches[0] || e.changedTouches[0]
	}
	var pageX = touch.pageX;
	var left = pageX - state.startX + (state.lastLeft || 0);
	left = left < 0 ? 0 : left;
	var width = slideBarWidth - slideBlockWidth;
	left = left >= width ? width : left;
	state.startX = pageX
	state.lastLeft = left
	styleChange(left, ins)
}

function touchend(e, ins, event) {
	if (disabled) return;
	var state = {}
	if (isH5 && isPC()) {
		state = event.instance.getState()
	} else {
		state = e.instance.getState()
	}
	var left = slideBarWidth - slideBlockWidth
	if (left - state.lastLeft <= errorRange) {
		styleChange(left, ins)
		ins.callMethod('success')
	} else {
		state.startX = 0;
		state.lastLeft = 0;
		styleChange(0, ins)
	}
}

function slidereset(reset, oldreset, owner, ins) {
	var state = ins.getState()
	if (reset > 0) {
		state.startX = 0;
		state.lastLeft = 0;
		styleChange(0, owner)
	}
}
var movable = false;

function mousedown(e, ins) {
	if (!isH5 || !isPC()) return
	touchstart(e, ins)
	movable = true
	window.onmousemove = function(event) {
		if (!isH5 || !isPC() || !movable) return
		touchmove(event, ins, e)
	}
	window.onmouseup = function(event) {
		if (!isH5 || !isPC() || !movable) return
		touchend(event, ins, e)
		movable = false
	}
}

module.exports = {
	touchstart: touchstart,
	touchmove: touchmove,
	touchend: touchend,
	mousedown: mousedown,
	slidereset: slidereset
}