<template>
	<view class="container">
		<!-- 列表菜单 -->
		<view class="font-14">
			<view class="bg-color-fff marg-10 padd-10 border-radius-5">
				<view class="border-b-ddd padd-5_0 flex flex-both">
					<text class="font-bold font-16">基本信息</text>
				</view>
				<view class="padd-t-5">
					<view class="flex">
						<text class="flex-item-shrink-0">任务名称：</text>
						<text>{{dataInfo.inspectionPlanName}}</text>
					</view>
					<view class="flex flex-wrap">
						<text class="flex-item-shrink-0">任务时间：</text>
						<text>{{dataInfo.startTime }}~{{dataInfo.endTime}}</text>
					</view>
					<!-- <view>任务编号：{{dataInfo.taskNum}}</view> -->
					<view>巡检项：{{dataInfo.inspectionItemName}}</view>
					<view class="flex">
						<text class="flex-item-shrink-0">巡检人员：</text>
						<text>{{dataInfo.inspectionPersonnel}}</text>
					</view>
				</view>
			</view>

			<view class="bg-color-fff marg-10 padd-10 border-radius-5">
				<view class="border-b-ddd padd-5_0 flex flex-both">
					<text class="font-bold font-16">巡检填报</text>
				</view>
				<tui-list-view>
					<tui-list-cell arrow v-for="(item,index) in deviceOptions" :key="index" @click="fun_goPage(item)">
						<view class="flex flex-both">
							<text class="font-bold">{{item.text}}</text>
							<text class="padd-r-5">{{item.planStatus==1?'已填报':'未填报'}}</text>
						</view>
					</tui-list-cell>
				</tui-list-view>
			</view>
		</view>

		<!-- 底部提交按钮-待巡检 -->
		<tui-white-space height="140" v-if="dataInfo.taskCompleteState==1"></tui-white-space>
		<view class="posit-fixed bottom-0 width-all flex flex-around padd-10_0 bg-color-fff" v-if="dataInfo.taskCompleteState==1">
			<tui-button height="70rpx" width="160rpx" type="primary" @click="submit">提交</tui-button>
		</view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';

	export default {
		data() {
			return {
				isAdmin: this.tui.getUserInfo('isAdmin'),
				gid: '',
				dataInfo: {},
				deviceOptions: [
					// 	{text: '设备1',value: '1',checked:true,status:0},
					// 	{text: '设备2',value: '2',checked:false,status:1},
					// 	{text: '设备3',value: '3',checked:false,status:1},
				],
				deviceStateOptionsShow: false,
				deviceStateOptions: [
					{text: '完好',value: 0},
					{text: '损坏',value: 1},
				],

				formData: {
					// deviceId: '',
					// deviceIds: '',
					// deviceAllIds: [],
				},
				rules: [
					{name: "deviceId",rule: ["required"],msg: ["请勾选一项"]},
					// {name: "deviceIds",rule: ["required"],msg: ["请至少勾选一项"],
					// 	validator: [{msg: "请至少勾选一项",method: (value) => {
					// 		return value.length > 0;
					// 	}}]
					// },
				],
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.gid){
				this.gid = e.gid;
				this.fun_getDetail();//获取详情
				// this.fun_getDeviceList();//获取设备列表
			}
		},
		onShow() {
			this.fun_getDeviceList();//获取设备列表
		},
        //下拉刷新
        onPullDownRefresh() {
        	setTimeout(function () {
        		uni.stopPullDownRefresh();
        	}, 200);
        	// this.fun_getTotal();//
        },
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取详情
			fun_getDetail() {
				this.http.get(this.$apiUrl.inspectDetail, {
					data: {
						gid: this.gid,
					}
				}).then( ({object}) => {
					console.log('获取详情:',object);
					if(!object){
						this.tui.toast('数据不存在');
						return;
					}
					this.dataInfo = object;
				})
			},
			//获取设备列表
			fun_getDeviceList() {
				this.http.post(this.$apiUrl.inspectDetailDeviceList, {
					data: {
						planId: this.gid,
					}
				}).then( ({object}) => {
					console.log('获取设备列表:',object);
					this.deviceOptions = object?.records?.map(v=>{
						return {
							...v,
							value: v.deviceId,
							text: v.deviceName,
							// checked: false,
							status: v.status,//设备状态值
							planStatus: v.planStatus,//巡检状态值
							deviceFacilities: v.deviceFacilities,//设施类型(0:热力站,1:设备)
						}
					});
					// this.deviceOptions.forEach(item => {
					// 	// 如果设备状态为空,则默认设备状态为完好
					// 	if(!item.status){
					// 		item.status = 0;
					// 		item.deviceStateName = this.deviceStateOptions[0].text;
					// 	}else{
					// 		item.deviceStateName = this.deviceStateOptions.find(v=>v.value == item.status).text;
					// 	}
					// })
				})
			},
			//选择设备-多选
			// fun_deviceOptionsChange(e) {
			// 	this.formData.deviceAllIds = e.detail.value;
			// 	console.log('deviceOptionsChange', e,this.formData.deviceAllIds);
			// 	console.log('选择设备-this.deviceOptions:',this.deviceOptions)
			// 	// 排除回显时已选中的设备(即:排除已完成填报的设备)
			// 	this.formData.deviceIds = this.deviceOptions.filter(v=>this.formData.deviceAllIds.includes(v.value)&&v.checked!=true).map(v=>v.value);
			// 	console.log('formData.deviceIds:',this.formData.deviceIds);
			// },
			//选择设备-单选
			// fun_deviceOptionsChange(e) {
			// 	// this.formData.deviceId = e.detail.value;
			// 	// console.log('deviceOptionsChange', e,this.formData.deviceId);
			// },
			//选择设备-状态
			// fun_deviceStateOptionsChange(e,index) {
			// 	console.log('deviceStateOptionsChange', e);
			// 	this.deviceOptions[index].status = e.value;
			// 	this.deviceOptions[index].deviceStateName = e.text;
			// 	console.log('选择设备状态-this.deviceOptions:',this.deviceOptions)
			// },
			
			//提交
			submit() {
				// this.$refs.form.validate(this.formData, this.rules, true).then(res => {
				// 	if (res.isPass) {
						// this.formData.form_deviceOption = this.deviceOptions.filter(v=>this.formData.deviceIds.includes(v.value)&&v.checked!=true).map(v=>{return {value:v.value,status:v.status}});//过滤已填报的设备和重组只留value和status
						console.log('this.deviceOptions:',this.deviceOptions)
						const inspectDeviceOkArr = this.deviceOptions.filter(v=>v.planStatus == 1);
						console.log('inspectDeviceOkArr:',inspectDeviceOkArr);
						if(inspectDeviceOkArr.length != this.deviceOptions.length){
							this.tui.toast("请先完成未填报的设备")
							return;
						}

						this.http.get(this.$apiUrl.inspectDetailSubmit, {data:{
							planId: this.gid,
						}}).then(res => {
							console.log(res);
							this.tui.toast("提交成功")
							setTimeout(()=>{
								uni.navigateBack()
							},2000)
						})
				// 	} else {
				// 		console.log('验证不通过:',res)
				// 	}
				// }).catch(errors => {
				// 	console.log('验证失败:',errors)
				// })
			},

			//跳转
			fun_goPage(item) {
				const params = {
					planId: this.gid,
					planDetailsId: item.planDetailsId,
					taskCompleteState: this.dataInfo.taskCompleteState,//巡检状态
					deviceId: item.value,
					deviceName: item.text,
					status: item.status,//设备状态值
					planStatus: item.planStatus,//设备填报状态值
					deviceFacilities: item.deviceFacilities,//设施类型(0:热力站,1:设备)
					longitude: item.longitude||'',
					latitude: item.latitude||'',
					dangerNum: item.dangerStatus||0,
					maintNum: item.dealStatus||0,
				}
				uni.navigateTo({
					url: `/pages/inspect-manage/oneDevice?item=${JSON.stringify(params)}`

				});
			},
		},
	};
</script>

<style lang="scss">
    // page{background-color: #fff;}
	.tui-node {
		height: 44rpx;
		width: 44rpx;
		border-radius: 50%;
		background-color: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		flex-shrink: 0;
	}
</style>