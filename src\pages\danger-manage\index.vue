<template>
	<view class="container height-all flex flex-col">
		<tui-tab :tabs="tabs" :current="tabsKey" isFixed @change="fun_tabsChange" class="border-b-ddd"></tui-tab>
		<tui-white-space height="80"></tui-white-space>
		
		<!-- 列表菜单 -->
		<view class="font-14 flex-item-grow-1" style="min-height:0;height: 100%;">
			<mescroll-uni :fixed="false" ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
				<view class="bg-color-fff marg-t-10 padd-0_20" v-for="(item,key) of dataList" :key="key">
					<view class="padd-10_0" @click="fun_goInfo(item.maintenanceId)" v-if="!item.riskId">
						<view class=" border-b-ddd padd-b-5">上报时间：{{item.createTime}}</view>
						<view class="padd-t-5">
							<view class="flex flex-both">
								<view class="flex">
									<text class="flex-item-shrink-0">上报人：</text>
									<text>{{item.createUserName}}</text>
								</view>
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="maintTagType[item.status]" shape="circleLeft" padding="6rpx 12rpx">{{dangerStatusOptions.find((v) => v.value == item.status)?.text}}</tui-tag>
								</view>
							</view>
							<view>隐患类型：{{dangerTypeOptions.find((v) => v.value == item.maintenanceType)?.text}}</view>
							<view>隐患等级：{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</view>
							<view class="flex">
								<text class="flex-item-shrink-0">隐患描述：</text>
								<text>{{item.descMsg}}</text>
							</view>
						</view>
					</view>
					<view class="padd-10_0" @click="fun_goInfo(item.maintenanceId)" v-else>
						<view class=" border-b-ddd padd-b-5">报警时间：{{item.tsHeatWarningRecord?.warningTime}}</view>
						<view class="padd-t-5">
							<view class="flex flex-both">
								<view class="flex">
									<text class="flex-item-shrink-0">报警等级：</text>
									<text>{{alarmLevelOptions.find((v) => v.value == item.tsHeatWarningRecord?.warningLevel)?.text}}</text>
								</view>
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="maintTagType[item.status]" shape="circleLeft" padding="6rpx 12rpx">{{dangerStatusOptions.find((v) => v.value == item.status)?.text}}</tui-tag>
								</view>
							</view>
							<view>归属名称：{{item.tsHeatWarningRecord?.relationName}}</view>
							<view>点位名称：{{item.tsHeatWarningRecord?.heatPositionName}}</view>
							<view>报警内容：{{item.tsHeatWarningRecord?.warningContent}}</view>
							<!-- <view>报警值：{{item.tsHeatWarningRecord?.warningValue}}</view> -->
							<!-- <view>报警点位置：{{'??'}}</view> -->
						</view>
					</view>
					<!-- <view class="padd-b-10 border-t-ddd padd-t-5 text-right" v-if="isAdmin&&(item.status=='0'||item.status=='2')">
						<tui-tag style="margin-left: 20rpx;" type="warning" plain v-if="item.status=='0'" @click="fun_goPage(item,'maint')">派发维修</tui-tag>
						<tui-tag style="margin-left: 20rpx;" type="green" plain v-else-if="item.status=='2'" @click="fun_goPage(item,'end')">办结</tui-tag>
					</view> -->
				</view>
			</mescroll-uni>
		</view>
		<!-- 底部提交按钮 -->
		<tui-white-space height="140" v-if="!isAdmin&&!inspectShowType"></tui-white-space>
		<view class="posit-fixed bottom-0 width-all" v-if="!isAdmin&&!inspectShowType">
			<tui-button type="primary" @click="fun_goAdd()">隐患上报</tui-button>
		</view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';

	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin],
		data() {
			return {
                isAdmin: this.tui.getUserInfo('isAdmin'),
				userId: this.tui.getUserInfo('userId'),
				tabsKey: 0,
				tabsId:'',
				tabs: [
					{name: "全部",id: ''},
					{name: this.tui.getUserInfo('isAdmin')?"待处理":"待派发",id: '0'},
					{name: this.tui.getUserInfo('isAdmin')?"已处理":"已办结",id: '4'},
				],
				dataList: [
					// {maintType:1,startTime:'2025-03-05 10:10:10',person:'人员名称',type:'类型',level:'3',desc:'描述'},
					// {maintType:2,startTime:'2025-03-05 10:10:10',level:'4',device:'设备',position:'位置',itemId:'5',alarmId:'6'},
				],
				upOption:{
					auto: false, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true(下拉加载的配置中的自动执行也默认true)
					// page: {
					//     num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
					//     size: 10, // 每页数据的数量
					//     time: null // 加载第一页数据服务器返回的时间; 防止用户翻页时,后台新增了数据从而导致下一页数据重复;
					// },
					toTop:{
						src:''
					},
					textNoMore: '-- 没有更多了 --', // 没有更多数据的提示文本
					empty: {
						// icon: "", // 图标路径
						tip: '暂无相关数据'
					}
				},
				downOption:{
					auto: false, // 是否在初始化完毕之后自动执行下拉刷新的回调; 默认true
				},

				dangerStatusOptions:[],//隐患状态
				dangerTypeOptions:[],//隐患类型
				dangerLevelOptions:[],//隐患等级
				alarmLevelOptions:[],//报警等级
				maintTagType: {
					0: 'warning',
					1: 'warning',
					2: 'primary',
					3: 'danger',
					4: 'green',
				},
				riskTagType: {
					1: 'light-brownish',
					2: 'light-brownish',
					3: 'light-brownish',
					4: 'light-orange',
					5: 'light-blue',
					6: 'light-green',
					7: 'light-danger',
					8: 'light-danger',
					9: 'light-green',
				},

				// 巡检跳转过来的回显
				inspectShowType: '',
				planId: '',
				deviceId: '',
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.tabsKey) {
				this.tabsKey = e.tabsKey;
				this.tabsId = this.tabs[e.tabsKey]?.id;
			}
			this.fun_getDicts();//获取字典

			//巡检跳转过来的回显
			if(e.inspectShowType) {
				this.inspectShowType = e.inspectShowType;
				this.planId = e.planId;
				this.deviceId = e.deviceId;
			}
		},
		onShow() {
			this.$nextTick(() => {
				this.dataList = []; // 先置空列表,显示加载进度
				this.mescroll.resetUpScroll(); // 再刷新列表数据(默认会重置page.num=1)
			});
		},
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取字典
			fun_getDicts() {
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"WXZT"}
				// }).then( ({data}) => {
				// 	console.log('隐患状态:',data)
				// 	this.dangerStatusOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHLX"}
				// }).then( ({data}) => {
				// 	console.log('隐患类型:',data)
				// 	this.dangerTypeOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHDJ"}
				// }).then( ({data}) => {
				// 	console.log('隐患等级:',data)
				// 	this.dangerLevelOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getBusinessDict+'BJZT').then( ({data}) => {
				// 	console.log('报警状态:',data)
				// 	this.bjztOption = data;
				// })
				this.dangerStatusOptions = this.$apiUrl.localCommonDict.dangerStatusOptions;
				this.dangerTypeOptions = this.$apiUrl.localCommonDict.dangerTypeOptions;
				this.dangerLevelOptions = this.$apiUrl.localCommonDict.dangerLevelOptions;
				this.alarmLevelOptions = this.$apiUrl.localCommonDict.alarmLevelOptions;
			},
			fun_tabsChange(v) {
				console.log('fun_tabsChange',v.index,v.item.id);
				this.tabsKey = v.index;
				this.tabsId = v.item.id;
				this.dataList = [];
				this.mescroll.resetUpScroll();
			},
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				setTimeout(()=>{
				console.log('upCallback')
				let params = {
					status: this.tabsId,
				};
				if(!this.isAdmin){
					params.createUserId = this.userId;
				}
				//巡检跳转过来的回显
				if(this.inspectShowType == 'danger'){
					params.planId = this.planId;
					params.deviceId = this.deviceId;
				}

				//联网加载数据
				// apiGoods(page.num, page.size).then( ({data})=>{
				this.http.post(this.$apiUrl.maintList, { data:{pageNum:page.num, pageSize:page.size,...params} }).then( ({object}) => {
					//方法二(推荐): 后台接口有返回列表的总数据量 totalSize
					this.mescroll.endBySize(object.records.length, object.total); //必传参数(当前页的数据个数, 总数据量)
					//方法四 (不推荐),会存在一个小问题:比如列表共有20条数据,每页加载10条,共2页.如果只根据当前页的数据个数判断,则需翻到第三页才会知道无更多数据
					// this.mescroll.endSuccess(object.records.length);

					//设置列表数据
					if(page.num == 1) this.dataList = []; //如果是第一页需手动制空列表
					this.dataList=this.dataList.concat(object.records); //追加新数据
				}).catch((err)=>{
					console.log('upCallback_err',err)
					//联网失败, 结束加载
					this.mescroll.endErr();
				})
				},300)
			},
			fun_goInfo(id) {
				if(!this.inspectShowType){
					uni.navigateTo({
						url: `/pages/maint-manage/look?gid=${id}&isFromDanger=true`
					});
				}else{
					//巡检跳转过来的回显
					uni.navigateTo({
						url: `/pages/maint-manage/look?gid=${id}&isFromDanger=true&inspectShowType=&${this.inspectShowType}`
					});
				}
			},
			fun_goAdd() {
				uni.navigateTo({
					url: '/pages/maint-manage/add?isFromDanger=true'
				});
			},
			fun_goPage(item,type) {
				uni.navigateTo({
					url: `/pages/event-handle/event-handle?gid=${item.maintenanceId}&type=${type}&deviceId=${item.deviceId}&deviceName=${item.deviceName}&from=danger`

				});
			},
		},
	};
</script>

<style lang="scss">
	// page{background-color: #fff;}
	uni-page-body{height: 100%;}
</style>