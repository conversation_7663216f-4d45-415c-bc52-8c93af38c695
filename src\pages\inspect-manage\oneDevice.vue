<template>
	<view class="container">
		<!-- 列表菜单 -->
		<view class="font-14">
			<view class="bg-color-fff marg-10 padd-10 border-radius-5">
				<view class="border-b-ddd padd-5_0 flex flex-both">
					<text class="font-bold font-16">设备信息</text>
				</view>
				<view class="padd-t-5">
					<view class="flex">
						<text class="flex-item-shrink-0">设备名称：</text>
						<text>{{dataInfo.deviceName}}</text>
					</view>
					<view class="flex">
						<text class="flex-item-shrink-0">所属区域：</text>
						<text v-if="dataInfo.deviceFacilities == '0'">{{oneDeviceOfArea.stationName+oneDeviceOfArea.equipmentUnitName}}</text>
						<text v-else>{{oneDeviceOfArea.communityName+(oneDeviceOfArea.buildingName||'')+(oneDeviceOfArea.buildingUnitName||'')+(oneDeviceOfArea.heatUserName||'')}}</text>
					</view>
				</view>
			</view>

			<view class="bg-color-fff marg-10 padd-10 border-radius-5">
				<view class="border-b-ddd padd-5_0 flex flex-both">
					<text class="font-bold font-16">巡检填报</text>
				</view>
				<tui-form :tip-top="0" ref="form" :show-message="false" :model="formData">
					<tui-form-item label="设备状态" prop="deviceStateId" asterisk arrow>
						<tui-input v-model="formData.deviceStateName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="dataInfo.planStatus!=1&&dataInfo.taskCompleteState==1&&(deviceStateOptionsShow = true)"></tui-input>
						<tui-picker :pickerData="deviceStateOptions" :show="deviceStateOptionsShow" @hide="deviceStateOptionsShow = false" @change="fun_deviceStateOptionsChange"></tui-picker>
					</tui-form-item>
				</tui-form>
			</view>
		</view>
		<!-- 回显 v-if="dataInfo.taskCompleteState!=1" -->
		<view class="bg-color-fff marg-10 padd-10 border-radius-5" v-if="dataInfo.planStatus==1">
			<!-- 地图回显 -->
			<map v-if="dataInfo.longitude&&dataInfo.latitude" class="border-ddd marg-b-10" style="width: 100%; height: 300px;" :longitude="dataInfo.longitude" :latitude="dataInfo.latitude" :markers="[{id:1,longitude: dataInfo.longitude,latitude: dataInfo.latitude}]"></map>
			<!-- 隐患上报和维修数量 -->
			<view class="flex flex-both">
				<view class="flex flex-middle" @click="fun_goPageShow('danger')">
					<view class="">隐患上报：</view>
					<text class="color-blue">{{dataInfo.dangerNum}}</text>
				</view>
				<view class="flex flex-middle" @click="fun_goPageShow('maint')">
					<view class="">维修：</view>
					<text class="color-blue">{{dataInfo.maintNum}}</text>
				</view>
			</view>
		</view>

		<!-- 底部提交按钮-待巡检 -->
		<tui-white-space height="140" v-if="dataInfo.taskCompleteState==1&&dataInfo.planStatus!=1"></tui-white-space>
		<view class="posit-fixed bottom-0 width-all flex flex-around padd-10_0 bg-color-fff" v-if="dataInfo.taskCompleteState==1&&dataInfo.planStatus!=1">
			<tui-button height="70rpx" width="160rpx" type="warning" @click="fun_goAdd('danger')">隐患上报</tui-button>
			<tui-button height="70rpx" width="160rpx" type="danger" @click="fun_goAdd('maint')">维修</tui-button>
			<tui-button height="70rpx" width="160rpx" type="primary" @click="submit">完成</tui-button>
		</view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';

	export default {
		data() {
			return {
				dataInfo: {},
				oneDeviceOfArea:{},
				deviceOptions: [
					// 	{text: '设备1',value: '1',checked:true,status:1},
					// 	{text: '设备2',value: '2',checked:false,status:2},
					// 	{text: '设备3',value: '3',checked:false,status:2},
				],
				deviceStateOptionsShow: false,
				deviceStateOptions: [
					{text: '完好',value: '1'},
					{text: '损坏',value: '2'},
				],

				formData: {
					deviceId: '',
					deviceStateId: '',
					deviceStateName: '',

					// deviceIds: '',
					// deviceAllIds: [],
				},
				rules: [
					{name: "deviceStateId",rule: ["required"],msg: ["请选择设备状态"]},
					// {name: "deviceIds",rule: ["required"],msg: ["请至少勾选一项"],
					// 	validator: [{msg: "请至少勾选一项",method: (value) => {
					// 		return value.length > 0;
					// 	}}]
					// },
				],
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.item){
				this.dataInfo = JSON.parse(e.item);
				//回显
				if(this.dataInfo.status=='1'||this.dataInfo.status=='2'){
					this.formData.deviceStateId = this.dataInfo.status.toString();
					this.formData.deviceStateName = this.deviceStateOptions.filter(v=>v.value==this.dataInfo.status)[0].text;
				}
				this.fun_getOneDeviceOfArea();//获取设备所属信息
			}
		},
        //下拉刷新
        onPullDownRefresh() {
        	setTimeout(function () {
        		uni.stopPullDownRefresh();
        	}, 200);
        	// this.fun_getTotal();//
        },
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取设备所属信息
			fun_getOneDeviceOfArea() {
				if(this.dataInfo.deviceFacilities == '0'){
					//热力站
					this.http.get(this.$apiUrl.getDeviceOfHeatStation, {
						data: {
							keyId: this.dataInfo.deviceId,
						}
					}).then( ({object}) => {
						console.log('获取热力站设备所属信息:',object);
						this.oneDeviceOfArea = {
							stationId: object.equipmentUnit?.station?.stationId||'',
							stationName: object.equipmentUnit?.station?.stationName||'',
							equipmentUnitId: object.equipmentUnit?.equipmentUnitId||'',
							equipmentUnitName: object.equipmentUnit?.equipmentUnitName||'',
						};
					})
				}else{
					//小区设备
					this.http.get(this.$apiUrl.getDeviceOfCommunity, {
						data: {
							id: this.dataInfo.deviceId,
						}
					}).then( ({object}) => {
						console.log('获取设备所属信息:',object);
						this.oneDeviceOfArea = object;
					})
				}
			},
			//选择设备-多选
			// fun_deviceOptionsChange(e) {
			// 	this.formData.deviceAllIds = e.detail.value;
			// 	console.log('deviceOptionsChange', e,this.formData.deviceAllIds);
			// 	console.log('选择设备-this.deviceOptions:',this.deviceOptions)
			// 	// 排除回显时已选中的设备(即:排除已完成填报的设备)
			// 	this.formData.deviceIds = this.deviceOptions.filter(v=>this.formData.deviceAllIds.includes(v.value)&&v.checked!=true).map(v=>v.value);
			// 	console.log('formData.deviceIds:',this.formData.deviceIds);
			// },
			//选择设备-单选
			// fun_deviceOptionsChange(e) {
			// 	this.formData.deviceId = e.detail.value;
			// 	console.log('deviceOptionsChange', e,this.formData.deviceId);
			// },
			//选择设备-状态
			fun_deviceStateOptionsChange(e) {
				console.log('deviceStateOptionsChange', e);
				this.formData.deviceStateId = e.value;
				this.formData.deviceStateName = e.text;
			},

			//获取当前位置信息
			fun_getLocation() {
				return new Promise((resolve, reject) => {
					uni.getLocation({
						type: 'gcj02',//默认:谷歌坐标:wgs84,国测局坐标(高德,百度,腾讯,及map组件):gcj02
						success: (res) => {
							console.log('当前位置的经度：' + res.longitude);
							console.log('当前位置的纬度：' + res.latitude);
							resolve(res);
						},
						fail: (err) => {
							console.log('获取位置失败:',err);
							reject(err);
						}
					})
				})
			},
			
			//提交
			async submit() {
				this.$refs.form.validate(this.formData, this.rules, true).then( async (res) => {
					if (res.isPass) {
						// this.formData.form_deviceOption = this.deviceOptions.filter(v=>this.formData.deviceIds.includes(v.value)&&v.checked!=true).map(v=>{return {value:v.value,status:v.status}});//过滤已填报的设备和重组只留value和status
						// console.log('this.formData:',this.formData)
						//当前位置信息
						let position = {};
						try{
							position = await this.fun_getLocation();//获取当前位置信息
						}catch(err){
							console.log('获取位置失败:',err);
						}

						this.http.post(this.$apiUrl.inspectDetailOneDeviceSubmit, {
							data:{
								planId: this.dataInfo.planId,
								planDetailsId: this.dataInfo.planDetailsId,
								// deviceId: this.dataInfo.deviceId,//使用planDetailsId了
								status: this.formData.deviceStateId,
								planStatus: 1,//设备填报状态值
								// deviceStateName: this.formData.deviceStateName,
								longitude: position?.longitude,
								latitude: position?.latitude,
							}
						}).then(res => {
							console.log(res);
							this.tui.toast("提交成功")
							setTimeout(()=>{
								uni.navigateBack()
							},2000)
						})
					} else {
						console.log('验证不通过:',res)
					}
				}).catch(errors => {
					console.log('验证失败:',errors)
				})
			},

			// 跳转-隐患上报,维修上报
			fun_goAdd(type){
				// this.$refs.form.validateField('deviceIds');//手动触发表单验证(V2.9.2+才有此方法)
				//手动触发表单验证
				// this.$refs.form.validate(this.formData, this.rules, true).then(res=>{
				// 	if (res.isPass) {
						if(type == 'danger'){
							//隐患上报
							uni.navigateTo({
								url: `/pages/maint-manage/add?inspectTaskDialogType=${type}&planId=${this.dataInfo.planId}&deviceId=${this.dataInfo.deviceId}`
							});
						}else{
							//维修上报
							uni.navigateTo({
								url: `/pages/maint-manage/add?inspectTaskDialogType=${type}&planId=${this.dataInfo.planId}&deviceId=${this.dataInfo.deviceId}`
							});
						}
				// 	} else {
				// 		console.log('验证不通过:',res)
				// 	}
				// }).catch(errors => {
				// 	console.log('验证失败:',errors)
				// })
			},
			// 跳转-隐患,维修的列表回显
			fun_goPageShow(type){
				if(type == 'danger'){
					//隐患上报
					uni.navigateTo({
						url: `/pages/danger-manage/index?inspectShowType=${type}&planId=${this.dataInfo.planId}&deviceId=${this.dataInfo.deviceId}`
					});
				}else{
					//维修上报
					uni.navigateTo({
						url: `/pages/maint-manage/index?inspectShowType=${type}&planId=${this.dataInfo.planId}&deviceId=${this.dataInfo.deviceId}`
					});
				}
			},

		},
	};
</script>

<style lang="scss">
    // page{background-color: #fff;}
	.tui-node {
		height: 44rpx;
		width: 44rpx;
		border-radius: 50%;
		background-color: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		flex-shrink: 0;
	}
</style>