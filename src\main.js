import App from './App'
import store from './store'
import tui from './common/tuiCommon.js'
import http from './common/httpRequest.js'
import propsConfig from './components/thorui/tui-config/index'
import apiUrl from './common/api.js'

//全局组件配置
uni.$tui = propsConfig

// #ifndef VUE3
import Vue from 'vue'
Vue.config.productionTip = false
Vue.prototype.http = http
Vue.prototype.tui = tui
Vue.prototype.$store = store
App.mpType = 'app'

const app = new Vue({
	store,
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'

export function createApp() {
	const app = createSSRApp(App)
	app.use(store)
	app.config.globalProperties.tui = tui;
	app.config.globalProperties.http = http;
	app.config.globalProperties.$apiUrl = apiUrl;
	return {
		app
	}
}
// #endif
