<script>
export default {
	onLaunch: function() {
		let that = this;
		// #ifdef APP-PLUS
		 /* 5+环境锁定屏幕方向 */
		 plus.screen.lockOrientation('portrait-primary'); //锁定
		// #endif
		
		// #ifdef MP-WEIXIN
		if (wx.canIUse('getUpdateManager')) {
			const updateManager = wx.getUpdateManager();
			updateManager.onCheckForUpdate(function(res) {
				// 请求完新版本信息的回调
				if (res.hasUpdate) {
					updateManager.onUpdateReady(function() {
						that.tui.modal('更新提示', '新版本已经上线啦~，为了获得更好的体验，建议立即更新', false, res => {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate();
						});
					});
					updateManager.onUpdateFailed(function() {
						// 新的版本下载失败
						that.tui.modal('更新失败', '新版本更新失败，为了获得更好的体验，请您删除当前小程序，重新搜索打开', false, res => {});
					});
				}
			});
		}
		// #endif
	},
	onShow: function() {},
	onHide: function() {}
};
</script>

<style>
@import './common/comm.scss';
@import './static/style/thorui-extend.css';
</style>