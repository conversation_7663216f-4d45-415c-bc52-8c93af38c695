/**
 * 请求拦截配置
 * <AUTHOR> 
 **/

import http from '../components/common/tui-request'
import tui from './tuiCommon.js'
import {ApiSite} from './serverConfig.js'//接口地址配置


//初始化请求配置项
http.create({
	host: ApiSite,
	header: {
		"Accept":"application/json; charset=utf-8",
		// 'content-type': 'application/x-www-form-urlencoded'
		// 'content-type': 'application/json;charset=utf-8'
	}
})
//请求拦截
http.interceptors.request.use(config => {
	let token = tui.getToken();
	if (config.header&&token) {
		config.header['Authorization'] = "Bearer " + token
	} else if(token){
		config.header = {
			'Authorization': "Bearer " + token
		}
	}
	return config
})

//响应拦截
let loginTimeOut=false;//登录超时
http.interceptors.response.use(response => {
	// console.log('响应拦截:',response);
	const {statusCode} = response; //浏览器状态码
	const {code,status} = response.data
	if(code === 200||code === 0){
		return response.data
	}
	else if(!code){
		if(!response.data.error){
			return response.data
		}
		else if(status == 401||statusCode === 401){
			//登录超时
			if(loginTimeOut){
				return false;
			}else{
				loginTimeOut=true;
			}
			tui.Fun_loginOut();//退出登录
			uni.showModal({
				title: '提示',
				content: '登录已失效',
				confirmText:'重新登录',
				showCancel:false,
				success: (res)=> {
					loginTimeOut=false;
					uni.reLaunch({
						url:'/pages/login/index',
					})
				}
			});
			return false;
		}
		else if(status === 403||statusCode === 403){
			tui.toast('无权限访问');
			return Promise.reject(response.data)
		}
		else if(status === 404||statusCode === 404){
			tui.toast('资源未找到');
			return Promise.reject(response.data)
		}
		else if(status === 500||statusCode === 500){
			tui.toast('服务器错误');
			return Promise.reject(response.data)
		}
		else if(status === 502||statusCode === 502){
			tui.toast('网关错误');
			return Promise.reject(response.data)
		}
		else if(status === 503||statusCode === 503){
			tui.toast('服务不可用');
			return Promise.reject(response.data)
		}
		else{
			tui.toast(response.data.error);
			return Promise.reject(response.data)
		}
	}
    else{
        tui.toast(response.data.message||response.data.msg||'异常错误');
		return Promise.reject(response.data)
	}
})



export default http
