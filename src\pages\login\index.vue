<template>
	<view class="content flex-c">
		<image class="logo box-shadow-filter" src="/static/images/logo.png"></image>
		<!-- <view class="text-area">
			<text class="title">{{title}}</text>
		</view> -->
		
		<view>
			<view class="input-item">
				<text class="">用户名</text>
				<input class="border-b-ddd width-all min-height-36"
					:value="username"
					placeholder="请输入用户名"
					data-key="username"
					@input="inputChange"
				/>
			</view>
			<view class="input-item">
				<text class="">密码</text>
				<!-- @change是兼容苹果12及以上密码自动填充时偶尔填充不上的bug -->
				<input class="border-b-ddd width-all min-height-36"
					:value="password" 
					placeholder-class="input-empty"
					maxlength="20"
					placeholder="请输入密码"
					password 
					data-key="password"
					@input="inputChange"
					@focus="inputFocus"
					@change="inputChange"
				/>
				<view class="color-red" v-show="patternPsdErr">{{patternPsdMsg}}</view>
			</view>
			<!-- <view class="input-checkbox">
				<u-checkbox
					@change="checkboxChange" 
					v-model="isChecked" 
				>记住密码</u-checkbox>
			</view> -->
		</view> 
		<view class="marg-auto" style="width:400rpx;padding-top: 100rpx;">
            <tui-button shape="circle" @click="toLogin()" :loading="logining" :disabled="logining">登 录</tui-button>
        </view>
	</view>
</template>


<script>
	import { h } from 'vue';
import { mapState, mapMutations } from 'vuex';
	
	// import md5 from '../../utils/md5.js';
	
	export default {
		computed: {
		    ...mapState(['hasLogin','setting'])
		},
		data() {
			return {
				title: '登录',
				
				username: '',
				password: '',
				logining: false,
				wxEnvironment:'miniSystem',
				wxSelfKey:'',
				isChecked:false,
				patternPsdErr:false,
				patternPsdMsg:'登录失败5次，请30分钟后再次尝试！',
			}
		},
		onLoad() {
            // this._getSm2Enabled();//加密解密SM
			// uni.showLoading({
			// 	mask:true
			// })
		},
		onShow() {
			
		},
		methods: {
			...mapMutations(['gologin','setSetting','setPersionType']),
            //获取是否加密
            async _getSm2Enabled(){
                let {result} = await this.$u.get(this.$API.netRequest.getSm2Enabled)
                console.log('getSm2Enabled:',result)
                uni.setStorageSync("encryptRequestPaths", result.encryptRequestPaths)
                uni.setStorageSync("noEncryptRequestPaths", result.noEncryptRequestPaths)
                uni.setStorageSync("sm2Enabled", result.sm2Enabled)
                
                // this.getSystemKey()
                
                // 判断微信类型
                // try {
                // 	const res = wx.getSystemInfoSync()
                // 	console.log('wx.getSystemInfoSync',res)
                	
                // 	if(res.environment){
                // 		this.wxEnvironment = 'cpSystem';
                // 		this.wxQyLogin()
                // 	}else{
                // 		this.wxLogin();
                // 	}
                // }catch(e){}
                
                if( uni.getStorageSync('thorui_token') ){
                	console.log('autoLogin');
                	this.goHome()
                }
            },

			goHome(){
				uni.switchTab({
					url: '/pages/tabbar/index/index'
				})
			},
			checkboxChange(e){
				console.log('checkboxChange', e.value);
				this.isChecked=e.value
				uni.setStorageSync('isCheckedInfor', this.isChecked);
			},
			inputChange(e){
				const key = e.currentTarget.dataset.key;
				console.log(key)
				this[key] = e.detail.value;
			},
			inputFocus(e){
				console.log('获取焦点',e)
				if(uni.getStorageSync('password')){
					this.password=''
					uni.setStorageSync('password','')
				}
			},
			async toLogin(){
				if(this.username == '' || this.password == ''){
					uni.showToast({
						mask: true,
						icon:'none',
						title:'请输入用户名或密码',
					})
					return;
				}
				 
				this.logining = true;
				setTimeout(()=>{
					this.logining=false;
				},1000)
				this.http.post(this.$apiUrl.jlt_Login, {
                    data:{
						"clientId":"phone",
        				"clientSecret":"smart-app-2020",
                        username:this.username,
                        // password:uni.getStorageSync('password')?this.password:md5(this.password),
                        password:this.password,
                    },
					header:{
						'content-type': 'application/x-www-form-urlencoded'
					}
                }).then( (data) => {
					console.log('登录成功:',data)
					console.log('==============')
					console.log(data.access_token)
					// if(this.isChecked){
						// 	// uni.setStorageSync('password', uni.getStorageSync('password')?this.password:md5(this.password))
						// 	uni.setStorageSync('password', this.password)
						// }else{
					// 	uni.setStorageSync('password', '')
					// };
					this.tui.setToken(data.access_token);
					//获取用户信息
					this.http.get(this.$apiUrl.getUserInfo).then( (object) => {
						console.log('用户信息:',object)
						this.tui.setUserInfo({
							userId: object.userId,
							userName: object.loginName,
							userRealName: object.realName,
							mobile: object.mobilePhone,
							isAdmin: object.roles.findIndex(v => v.roleCode == 'admin')!= -1?true:false,
						});
						this.tui.toast('登录成功');
						setTimeout(() => {
							uni.switchTab({
								url:'/pages/tabbar/index/index'
							})
						}, 1500);
					})
				})
				/* .catch(err => {
                    console.log(err);
                    if (err.code == 1032) {
                        this.patternPsdErr = true; //显示错误5次锁定30分钟
                        if(err.msg)this.patternPsdMsg=err.msg;
                    }
                }); */
			},
		}
	}
</script>



<style lang="scss" scoped>
.content {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.logo {
	height: 200rpx;
	width: 200rpx;
	margin: 120rpx 0;
	// border-radius: 50%;
	// box-shadow: 0 0 8px 0 #d4d4d4;
}

.text-area {
	display: flex;
	justify-content: center;
	margin-bottom: 30rpx;
}

.title {
	font-size: 36rpx;
	color: #8f8f94;
}

.input-item{ 
	display:flex;
	flex-direction: column;
	align-items:flex-start;
	justify-content: center;
	padding: 0 30upx;
	width: 600rpx;
	height: 120upx;
	border-radius: 4px;
	margin-bottom: 50upx;
}
.input-checkbox{ 
	display:flex;
	flex-direction: column;
	align-items:flex-start;
	justify-content: center;
	padding: 0 30upx;
	width: 600rpx;
	border-radius: 4px;
	margin-bottom: 30upx;
}
</style>

