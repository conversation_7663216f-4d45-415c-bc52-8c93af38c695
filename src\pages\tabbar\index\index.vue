<template>
	<view class="container">
		<!-- 顶部自定义标题 -->
		<tui-navigation-bar title="首页" color="#fff" :transparent="true" :splitLine="false">
			<!-- <tui-icon name="notice-fill" color="#fff" size="24" class="padd-10"></tui-icon> -->
		</tui-navigation-bar>
		<!-- 头部菜单 -->
        <view class="posit-relat z-index-1 bg-color-body">
            <view class="posit-absol top-0 left-0 width-all z-index--1" style="top:-50rpx;">
                <image class="width-all" mode="top" :style="{height:'420rpx'}" src="~@/static/images/index/head_bg.jpg"></image>
            </view>
            <view class="padd-20" style="margin-top: 200rpx;">
                <view class="bg-color-fff border-radius-15 box-shadow flex flex-around text-center font-14" style="padding: 10rpx 0 20rpx 0;">
                    <view class="" v-for="(item,key) of menuList" :key="key">
                        <view class="text-center padd-5_0 posit-relat" @click="fun_menuListClick(item.goPage)">
							<!-- <text class="block font-26 font-bold" style="min-height:70rpx;" :style="{color:item.color}" v-if="item.value!=null">{{item.value}}</text> -->
							<image style="width:120rpx;height:120rpx;" :src="`/static/images/index/index_icon${key}.png`"></image>
                            <text class="block">{{item.name}}</text>
							<tui-badge type="danger" absolute :scaleRatio="0.8" translateX="40%" top="20rpx" right="20rpx" v-if="item.value!=null">{{item.value}}</tui-badge>
                        </view>
                    </view>
                </view>
            </view>
        </view>
		<!-- 列表菜单 -->
        <view class="bg-color-fff font-14" v-if="!isAdmin">
			<view class="border-b-ddd">
				<tui-list-cell arrow>
					<view class="flex flex-both">
						<text class="font-bold">待巡检</text>
						<text class="color-999 padd-r-5" @click="fun_menuListClick(menuList[0].goPage)">更多</text>
					</view>
				</tui-list-cell>
				<view class="padd-0_20">
					<view class="padd-10_0 border-b-ddd last-no-border" v-for="(item,key) of inspectList" :key="key" @click="fun_goInspectInfo(item.planId)">
						<view class="flex flex-both">
							<text>{{item.inspectionPlanName}}</text>
							<text class="flex-item-shrink-0 color-blue">{{item.time}}</text>
						</view>
						<view>巡检项：{{item.inspectionItemName}}</view>
						<view>开始时间：{{item.startTime}}</view>
						<view>截止时间：{{item.endTime}}</view>
					</view>
					<view class="padd-10_0 text-center" v-if="inspectList.length===0">
						<text class="color-999">暂无数据</text>
					</view>
				</view>
			</view>
			<view class="border-b-ddd">
				<tui-list-cell arrow>
					<view class="flex flex-both">
						<text class="font-bold">待维修</text>
						<text class="color-999 padd-r-5" @click="fun_menuListClick(menuList[2].goPage)">更多</text>
					</view>
				</tui-list-cell>
				<view class="padd-0_20">
					<view class="padd-10_0 border-b-ddd last-no-border" v-for="(item,key) of maintList" :key="key" @click="fun_goMaintInfo(item.maintenanceId)">
						<view v-if="!item.riskId">
							<view class="flex flex-both">
								<text>{{item.createTime}}</text>
								<!-- <text class="flex-item-shrink-0 color-blue">{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</text> -->
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="dangerLevelTagType[item.level]" shape="circleLeft" padding="6rpx 12rpx">{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</tui-tag>
								</view>
							</view>
							<view>隐患名称：{{item.maintenanceName}}</view>
							<view>所属设备：{{item.deviceName}}</view>
							<view>隐患描述：{{item.descMsg}}</view>
						</view>
						<view v-else>
							<view class="flex flex-both">
								<text>{{item.tsHeatWarningRecord?.warningTime}}</text>
								<!-- <text class="flex-item-shrink-0 color-blue">{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</text> -->
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="alarmLevelTagType[item.tsHeatWarningRecord?.warningLevel]" shape="circleLeft" padding="6rpx 12rpx">{{alarmLevelOptions.find((v) => v.value == item.tsHeatWarningRecord?.warningLevel)?.text}}</tui-tag>
								</view>
							</view>
							<view>归属名称：{{item.tsHeatWarningRecord?.relationName}}</view>
							<view>点位名称：{{item.tsHeatWarningRecord?.heatPositionName}}</view>
							<view>报警内容：{{item.tsHeatWarningRecord?.warningContent}}</view>
							<view>报警值：{{item.tsHeatWarningRecord?.warningValue}}</view>
							<!-- <view>报警点位置：{{'??'}}</view> -->
						</view>
					</view>
					<view class="padd-10_0 text-center" v-if="maintList.length===0">
						<text class="color-999">暂无数据</text>
					</view>
				</view>
			</view>
        </view>
		<view class="bg-color-fff font-14" v-else>
			<view class="border-b-ddd">
				<tui-list-cell arrow>
					<view class="flex flex-both">
						<text class="font-bold">实时报警(报警中)</text>
						<text class="color-999 padd-r-5" @click="fun_menuListClick(menuList[3].goPage1)">更多</text>
					</view>
				</tui-list-cell>
				<view class="padd-0_20">
					<view class="padd-10_0 border-b-ddd last-no-border" v-for="(item,key) of alarmList" :key="key" @click="fun_goAlarmInfo(item.heatWarningRecordId)">
						<view class="flex flex-both">
							<text>{{item.warningTime}}</text>
							<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
								<tui-tag :type="alarmLevelTagType[item.warningLevel]" shape="circleLeft" padding="6rpx 12rpx">{{alarmLevelOptions.find((v) => v.value == item.warningLevel)?.text}}</tui-tag>
							</view>
						</view>
						<view>归属名称：{{item.relationName}}</view>
						<view>点位名称：{{item.heatPositionName}}</view>
						<view>报警内容：{{item.warningContent}}</view>
						<!-- <view>报警点位置：{{'??'}}</view> -->
					</view>
					<view class="padd-10_0 text-center" v-if="alarmList.length===0">
						<text class="color-999">暂无数据</text>
					</view>
				</view>
			</view>
			<view class="border-b-ddd">
				<tui-list-cell arrow>
					<view class="flex flex-both">
						<text class="font-bold">待办</text>
						<text class="color-999 padd-r-5" @click="fun_menuListClick(menuList[3].goPage)">更多</text>
					</view>
				</tui-list-cell>
				<view class="padd-0_20">
					<view class="padd-10_0 border-b-ddd last-no-border" v-for="(item,key) of maintList" :key="key" @click="fun_goMaintInfo(item.maintenanceId,'awaitHandle')">
						<view v-if="!item.riskId">
							<view class="flex flex-both">
								<text>{{item.createTime}}</text>
								<!-- <text class="flex-item-shrink-0 color-blue">{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</text> -->
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="dangerLevelTagType[item.level]" shape="circleLeft" padding="6rpx 12rpx">{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</tui-tag>
								</view>
							</view>
							<view>隐患名称：{{item.maintenanceName}}</view>
							<view>所属设备：{{item.deviceName}}</view>
							<view>隐患描述：{{item.descMsg}}</view>
							<view>状态：{{dangerStatusOptions.find((v) => v.value == item.status)?.text}}</view>
						</view>
						<view v-else>
							<view class="flex flex-both">
								<text>{{item.tsHeatWarningRecord?.warningTime}}</text>
								<!-- <text class="flex-item-shrink-0 color-blue">{{dangerLevelOptions.find((v) => v.value == item.level)?.text}}</text> -->
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="alarmLevelTagType[item.tsHeatWarningRecord?.warningLevel]" shape="circleLeft" padding="6rpx 12rpx">{{alarmLevelOptions.find((v) => v.value == item.tsHeatWarningRecord?.warningLevel)?.text}}</tui-tag>
								</view>
							</view>
							<view>归属名称：{{item.tsHeatWarningRecord?.relationName}}</view>
							<view>点位名称：{{item.tsHeatWarningRecord?.heatPositionName}}</view>
							<view>报警内容：{{item.tsHeatWarningRecord?.warningContent}}</view>
							<!-- <view>报警值：{{item.tsHeatWarningRecord?.warningValue}}</view> -->
							<!-- <view>报警点位置：{{'??'}}</view> -->
							<view>状态：{{dangerStatusOptions.find((v) => v.value == item.status)?.text}}</view>
						</view>
					</view>
					<view class="padd-10_0 text-center" v-if="maintList.length===0">
						<text class="color-999">暂无数据</text>
					</view>
				</view>
			</view>
        </view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';

    import dayjs from 'dayjs'
    import 'dayjs/locale/zh-cn'
    import relativeTime from 'dayjs/plugin/relativeTime'
    dayjs.extend(relativeTime)
	
	export default {
		data() {
			return {
                isAdmin: this.tui.getUserInfo('isAdmin'),
				userId: this.tui.getUserInfo('userId'),
				menuList: [
					{name:'巡检',value:null,goPage:'/pages/inspect-manage/index?tabsKey=1'},
					{name:'隐患上报',value:null,goPage:'/pages/danger-manage/index?tabsKey=1'},
					// {name:'维修',value:null,goPage:'/pages/maint-manage/index?tabsKey=1'},
					// {name:'报警',value:null,goPage:'/pages/alarm-manage/index?tabsKey=0'},
					// {name:'待办',value:null,goPage:'/pages/await-handle/index?tabsKey=0',goPage1:'/pages/await-handle/index?tabsKey=1'},
				],
				//待巡检
				inspectList: [
					// {id:1,name:'巡检任务名称',time:'1天3小时50分',content:'巡检项',startTime:'2025-03-05 10:10:10',endTime:'2025-08-14 16:10:10'},
					// {id:2,name:'巡检任务名称',time:'1天3小时50分',content:'巡检项',startTime:'2025-03-05 10:10:10',endTime:'2025-04-05 10:10:10'},
				],
				//待维修
				maintList: [
					// {id:1,reportTime:'2025-03-05 10:10:10',name:'隐患名称',level:'二级',device:'巡检项',desc:'描述'},
					// {id:2,reportTime:'2025-03-05 10:10:10',name:'隐患名称',level:'二级',device:'巡检项',desc:'描述'},
				],
				//实时报警
				alarmList: [
					// {id:1,reportTime:'2025-03-05 10:10:10',name:'隐患名称',level:'二级',device:'巡检项',desc:'描述'},
					// {id:2,reportTime:'2025-03-05 10:10:10',name:'隐患名称',level:'二级',device:'巡检项',desc:'描述'},
				],

				fpage:{
					pageNum: 1,
					pageSize: 10,
				},
				dangerLevelOptions:[],//隐患等级
				dangerStatusOptions:[],//隐患状态
				alarmLevelOptions:[],//报警等级
				dangerLevelTagType:{
					'1':'light-orange',
					'2':'warning',
					'3':'light-danger',
					'4':'danger',
				},
				alarmLevelTagType:{
					'-1':'light-orange',
					'0':'warning',
					'1':'light-danger',
					'2':'danger',
				},

				riskTagType: {
					1: 'light-brownish',
					2: 'light-brownish',
					3: 'light-brownish',
					4: 'light-orange',
					5: 'light-blue',
					6: 'light-green',
					7: 'light-danger',
					8: 'light-danger',
					9: 'light-green',
				},
			};
		},
		computed: mapState(['isOnline']),
		onLoad() {
			if(!this.isAdmin){
				if(!this.menuList[2]){
                    this.menuList.push({name:'维修',value:null,goPage:'/pages/maint-manage/index?tabsKey=1'});
                }
			}else{
                if(!this.menuList[2]){
                    this.menuList.push({name:'报警',value:null,goPage:'/pages/alarm-manage/index?tabsKey=0'});
                }
                if(!this.menuList[3]){
                    this.menuList.push({name:'待办',value:null,goPage:'/pages/await-handle/index?tabsKey=0',goPage1:'/pages/await-handle/index?tabsKey=1'});
                }
            }
			this.fun_getDicts();//获取字典表数据
		},
		onShow() {
			this.fun_getList();
        },
        //下拉刷新
        onPullDownRefresh() {
        	setTimeout(function () {
        		uni.stopPullDownRefresh();
        	}, 200);
        	this.fun_getList();
        },
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取字典表数据
			fun_getDicts() {
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHDJ"}
				// }).then( ({data}) => {
				// 	console.log('隐患等级:',data)
				// 	this.dangerLevelOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getBusinessDict+'BJZT').then( ({data}) => {
				// 	console.log('隐患状态:',data)
				// 	this.dangerStatusOptions = data;
				// })
				this.dangerLevelOptions = this.$apiUrl.localCommonDict.dangerLevelOptions;
				this.dangerStatusOptions = this.$apiUrl.localCommonDict.dangerStatusOptions;
				this.alarmLevelOptions = this.$apiUrl.localCommonDict.alarmLevelOptions;
			},
            fun_getList() {
				if(!this.isAdmin){
					this.http.post(this.$apiUrl.inspectList, {
						data:{...this.fpage,taskCompleteState:'1',inspectionPersonnelId:this.userId}
					}).then( ({object}) => {
						console.log('巡检任务列表:',object)
						// this.menuList[0].value = object.total;//点检巡查角标
						this.inspectList = object.records.slice(0,2);
						this.inspectList.forEach(item => {
							item.time = dayjs(item.endTime).locale('zh-cn').fromNow();
						})
					})
					this.http.post(this.$apiUrl.maintList, {
						data:{...this.fpage,status:1}
					}).then( ({object}) => {
						console.log('维修任务列表:',object)
						this.menuList[2].value = object.total;//维修角标
						this.maintList = object.records.slice(0,2);
					})
				}else{
					this.menuList[3].value = 0;
					this.http.post(this.$apiUrl.alarmList, {
						data:{
							...this.fpage,
							restoreFlag:'0',//报警状态:0-报警中,1-已恢复
							isRepairFlag:'0',//是否维修派发：1-是,0-否
						}
					}).then( ({object}) => {
						console.log('报警列表:',object)
						// this.menuList[2].value = object.total;//报警角标
						this.menuList[3].value += object.total;//待办角标
						this.alarmList = object.records.slice(0,1);
					})
					//status:0上报-待派发,1派发-待处置,2处置完成-待办结,3退回,4办结
					this.http.post(this.$apiUrl.maintList, {
						data:{...this.fpage,statusArray:['0','2']}
					}).then( ({object}) => {
						console.log('待办列表:',object)
						this.menuList[3].value += object.total;//待办角标
						this.maintList = object.records;
					})
				}
            },
			//头部菜单点击
			fun_menuListClick(page) {
				uni.navigateTo({
					url: page,
				});
			},
			//跳转巡检详情
			fun_goInspectInfo(id) {
				uni.navigateTo({
					url: `/pages/inspect-manage/look?gid=${id}`
				});
			},
			//跳转维修详情
			fun_goMaintInfo(id,type) {
				if(!type){
					uni.navigateTo({
						url: `/pages/maint-manage/look?gid=${id}`,
					});
				}else{
					uni.navigateTo({
						url: `/pages/maint-manage/look?gid=${id}&isFromDanger=true&isFromAwaitHandle=true`
					});
				}
			},
			//跳转报警详情
			fun_goAlarmInfo(id) {
				uni.navigateTo({
					url: `/pages/alarm-manage/look?gid=${id}`,
				});
			},
		},
		// onShareAppMessage: function(e) {
		// 	return {
		// 		title: '标题',
		// 	};
		// }
	};
</script>

<style lang="scss">
    // 自定义导航栏背景图片
    // ::v-deep.tui-navigation-bar{}
	page{background-color: #fff;}
</style>