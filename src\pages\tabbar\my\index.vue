<template>
	<view class="posit-relat" style="background-color:#f0f5f9;height: 100vh;">
		<view class="posit-relat z-index-1 padd-t-30 padd-b-20 marg-b-10">
			<view class="posit-absol top-0 bottom-0 left-0 right-0 z-index--1">
				<image class="width-all height-all" src="../../../static/images/my/head-bg.jpg"></image>
			</view>
			<view class="text-center color-fff font-bold padd-20">个人中心</view>
			<view class="padd-t-20">
				<view class="text-center">
					<image class="border-ddd border-radius-50" style="width:140rpx;height:140rpx;" src="../../../static/images/my/userphoto.png" mode="scaleToFill" />
				</view>
				<view class="text-center color-fff font-bold">
					{{userInfo.userName||''}}
				</view>
			</view>
		</view>
        <!-- 列表 -->
        <tui-list-view>
            <tui-list-cell arrow v-for="item in menuList" :key="item.id" @click="fun_menuClick(item)">
                <view class="flex flex-both">
                    <text class="font-bold">{{item.title}}</text>
                    <text class="padd-r-5" :style="{color:item.rightColor}" v-if="item.right">{{item.right}}</text>
                </view>
            </tui-list-cell>
        </tui-list-view>
		
		<view class="marg-auto" style="width:400rpx;padding-top: 100rpx;">
            <tui-button shape="circle" @click="fun_loginOut()">退出帐号</tui-button>
        </view>
	</view>
</template>

<script>
	import { mapState, mapMutations, mapActions } from 'vuex';
	
	export default {
		data() {
			return {
				userInfo:{},
				version:'1.0.0',
				menuList: [
					// {
					// 	id:9,
					// 	title: '个人信息',
					// 	url:'/pages/my/information'
					// },
					{
						id:2,
						title: '修改密码',
						url:'../../amendPsd/amendPsd',
				        // right: '推荐90天更换一次密码，更安全',
				        rightColor: 'red',
					},
					// {
					// 	id:98,
					// 	title: '关于程序',
					// 	url:'../../about/about'
					// },
					// {
					// 	id:99,
					// 	title: '清除缓存'
					// },
				],
				avatarThisSrc:''
			};
		},
        onShow() {
        
        },
        onLoad() {
            if(this.tui.isLogin()){
                this.userInfo.userName = this.tui.getUserInfo('userName');
            }else{
                this.tui.modal('提示','尚未登录,请重新登录',false,()=>{
                    this.tui.Fun_loginOut();//退出登录
                    uni.reLaunch({
                    	url:'/pages/login/index',
                    })
                })
        		return false;
        	}
        },
		methods: {
            fun_menuClick(item){
            	if(item.url){
            		uni.navigateTo({
            			url:item.url
            		})
            	}else if(item.id == 99){
            		uni.showModal({
            			title:'提示',
            			content:'确定清除缓存吗?',
            			confirmText:'立即清除',
            			success(res) {
            				if(res.confirm){
            					uni.clearStorageSync();//清除全部
            					uni.showToast({
            						mask: true,
            						icon:'success',
            						title:'清除成功'
            					})
            					uni.reLaunch({
            						url:'/pages/login/index',
            					})
            				}
            			}
            		})
            	}else{
            		uni.showModal({
            			title:'提醒',
            			content:'功能正在开发中，敬请期待',
            			showCancel:false
            		})
            	}
            },
            //退出登录
			fun_loginOut(){
                uni.showModal({
                	title: '提示',
                	content: '确认退出登录',
                	confirmText:'退出',
                	success: (res)=> {
                		if(res.confirm){
                			this.tui.Fun_loginOut();//退出登录
                			uni.reLaunch({
                				url:'/pages/login/index',
                			})
                		}
                	}
                });
            },
		}
	};
</script>

<style lang="scss">
	// page{background-color: #fff;}
</style>