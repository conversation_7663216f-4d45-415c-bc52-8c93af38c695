/**
 * 常用方法封装 请求，文件上传等
 * <AUTHOR> 
 **/

import {ApiSite,UploadShowSite} from './serverConfig.js'

const tui = {
	//接口地址
	interfaceUrl: function() {
		return ApiSite
	},
	toast: function(text, duration, success, mask = true) {
		uni.showToast({
			// #ifndef MP-ALIPAY
			duration: duration || 2000,
			// #endif
			mask: mask,
			title: text || "出错啦~",
			icon: success ? 'success' : 'none'
		})
	},
	modal: function(title, content, showCancel, callback, confirmColor, confirmText) {
		uni.showModal({
			title: title || '提示',
			content: content,
			showCancel: showCancel,
			cancelColor: "#555",
			confirmColor: confirmColor || "#5677fc",
			confirmText: confirmText || "确定",
			success(res) {
				if (res.confirm) {
					callback && callback(true)
				} else {
					callback && callback(false)
				}
			}
		})
	},
	isAndroid: function() {
		const res = uni.getSystemInfoSync();
		return res.platform.toLocaleLowerCase() == "android"
	},
	isPhoneX: function() {
		const res = uni.getSystemInfoSync();
		let iphonex = false;
		let models = ['iphonex', 'iphonexr', 'iphonexsmax', 'iphone11', 'iphone11pro', 'iphone11promax']
		const model = res.model.replace(/\s/g, "").toLowerCase()
		if (models.includes(model)) {
			iphonex = true;
		}
		return iphonex;
	},
	constNum: function() {
		let time = 0;
		// #ifdef APP-PLUS
		time = this.isAndroid() ? 300 : 0;
		// #endif
		return time
	},
	delayed: null,
	loadding: false,
	showLoading: function(title, mask = true) {
		uni.showLoading({
			mask: mask,
			title: title || '请稍候...'
		})
	},
	/**
	 * 请求数据处理
	 * @param string url 请求地址
	 * @param string method 请求方式
	 *  GET or POST
	 * @param {*} postData 请求参数
	 * @param bool isDelay 是否延迟显示loading
	 * @param bool isForm 数据格式
	 *  true: 'application/x-www-form-urlencoded'
	 *  false:'application/json'
	 * @param bool hideLoading 是否隐藏loading
	 *  true: 隐藏
	 *  false:显示
	 */
	request: async function(url, method, postData, isDelay, isForm, hideLoading) {
		//接口请求
		tui.loadding && uni.hideLoading();
		tui.loadding = false;
		if (!hideLoading) {
			if (isDelay) {
				tui.delayed = setTimeout(() => {
					tui.loadding = true
					tui.showLoading()
					clearTimeout(tui.delayed)
				}, 1000)
			} else {
				tui.loadding = true
				tui.showLoading()
			}
		}

		return new Promise((resolve, reject) => {
			uni.request({
				url: tui.interfaceUrl() + url,
				data: postData,
				header: {
					'content-type': isForm ? 'application/x-www-form-urlencoded' :
						'application/json',
					'Authorization': tui.getToken()
				},
				method: method, //'GET','POST'
				dataType: 'json',
				success: (res) => {
					clearTimeout(tui.delayed)
					tui.delayed = null;
					if (tui.loadding && !hideLoading) {
						uni.hideLoading()
					}
					resolve(res.data)
				},
				fail: (res) => {
					clearTimeout(tui.delayed)
					tui.delayed = null;
					tui.toast("网络不给力，请稍后再试~")
					reject(res)
				}
			})
		})
	},
	/**
	 * 上传文件
	 * @param string url 请求地址
	 * @param string src 文件路径
	 */
	uploadFile: function(url, src,formData) {
		tui.showLoading()
		return new Promise((resolve, reject) => {
			const uploadTask = uni.uploadFile({
				url: url,
				filePath: src,
				name: 'file',
				header: {
					'Authorization': "Bearer " + tui.getToken()
				},
				formData: {
					type:'inspection',
					...formData,
					// sizeArrayText:""
				},
				success: function(result) {
					uni.hideLoading()
					const resStr = result.data;
					let res = JSON.parse(resStr.replace(/\ufeff/g, "") || "{}")
					// if (d.code % 100 == 0) {
					// 	//返回图片地址
					// 	let fileObj = d.data;
					// 	resolve(fileObj)
					// } else {
					// 	that.toast(res.message);
					// }
					if(res.code == 200||res.code == 0){
						res.url = UploadShowSite + res.object
						res.fileUrl = UploadShowSite + res.object
						console.log('上传成功',res);
						resolve(res)
					}else{
						tui.toast(res.message||'上传失败');
						reject(res)
					}
				},
				fail: function(res) {
					that.toast(res.msg||'上传失败');
					reject(res)
				}
			})
		})
	},
	tuiJsonp: function(url, callback, callbackname) {
		// #ifdef H5
		window[callbackname] = callback;
		let tuiScript = document.createElement("script");
		tuiScript.src = url;
		tuiScript.type = "text/javascript";
		document.head.appendChild(tuiScript);
		document.head.removeChild(tuiScript);
		// #endif
	},
	//设置token
	setToken(token) {
		return uni.setStorageSync("thorui_token", token)
	},
	//获取token
	getToken() {
		return uni.getStorageSync("thorui_token")
	},
	//判断是否登录
	isLogin: function() {
		return uni.getStorageSync("thorui_token") ? true : false
	},
    //退出登录
    Fun_loginOut(){
        // uni.clearStorageSync();//清空本地缓存
        uni.removeStorageSync('thorui_token');//有记住密码功能所以退出登录要单个清理
		uni.removeStorageSync('userId');//有记住密码功能所以退出登录要单个清理
		uni.removeStorageSync('userRealName');//有记住密码功能所以退出登录要单个清理
		uni.removeStorageSync('userName');//有记住密码功能所以退出登录要单个清理
		uni.removeStorageSync('mobile');//有记住密码功能所以退出登录要单个清理
		uni.removeStorageSync('isAdmin');//有记住密码功能所以退出登录要单个清理
    },
	//设置用户信息
	setUserInfo: function(dataType,value) {
		if( typeof dataType == 'string'){  //传单个字段和值
			uni.setStorageSync(dataType, value)
		}else{  //传一个整体对象
			Object.entries(dataType).forEach( ([key,value]) => {
				uni.setStorageSync(key, value)
			})
		}
	},
	//获取用户信息
	getUserInfo(type){
		if(!type){  //不传参数，返回所有(一个对象)
			return {
				userId: uni.getStorageSync("userId"),
				userRealName: uni.getStorageSync("userRealName"),
				userName: uni.getStorageSync("userName"),
				mobile: uni.getStorageSync("mobile"),
				isAdmin: uni.getStorageSync("isAdmin"),
			}
		}else{  //传一个字段，返回对应的一个值
			return uni.getStorageSync(type)||''
		}
	},
	//跳转页面，校验登录状态
	href(url, isVerify) {
		if (isVerify && !tui.isLogin()) {
			uni.navigateTo({
				url: '/pages/common/login/login'
			})
		} else {
			uni.navigateTo({
				url: url
			});
		}
	},
	//图片放大
	fun_bigImg(v_url,bigImgArr){
		console.log('fun_bigImg:',v_url,bigImgArr);
		uni.previewImage({
			urls: bigImgArr, //["/static/logo.png"]需要预览的图片http链接列表
			current: v_url, // 当前显示图片的http链接，默认是第一个
			indicator: 'default',//(仅APP)指示器类型:"default"底部圆点,"number"顶部数字,"none"不显示
			success: (res)=> {},
			fail: (res)=> {},
			complete: (res)=> {},
		})
	},
}

export default tui
