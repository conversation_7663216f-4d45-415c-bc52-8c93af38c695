
page {
	background-color: #F3F7FA;
	font-size: 32rpx;
	font-family: -apple-system-font, Helvetica Neue, Helvetica, sans-serif;
}

.tui-page__hd {
	width: 100%;
	padding: 40px;
	box-sizing: border-box;
}

.tui-page__bd {
	padding-bottom: 40px;
}


.tui-page__title {
	text-align: left;
	font-size: 20px;
	font-weight: 400;
}

.tui-page__desc {
	margin-top: 5px;
	color: #888888;
	text-align: left;
	font-size: 14px;
}

.tui-page__spacing {
	padding-left: 15px;
	padding-right: 15px;
}

.tui-section__title {
	font-size: 28rpx;
	color: #999999;
	margin-bottom: 10rpx;
	padding: 30rpx 5px 20rpx;
	box-sizing: border-box;
}

::-webkit-scrollbar {
	width: 0 !important;
	height: 0 !important;
	color: transparent !important;
	display: none;
}

button::after {
	border: none;
}

.container {
	display: flex;
	box-sizing: border-box;
	flex-direction: column;
}

.tui-phcolor {
	color: #ccc;
	font-size: 32rpx;
	overflow: visible;
}

.tui-opcity {
	opacity: 0.5;
}

.tui-hover {
	background-color: #f7f7f9 !important;
}

.tui-ellipsis {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.tui-list-item {
	position: relative;
}

.tui-list-item::after {
	content: '';
	position: absolute;
	border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
	-webkit-transform: scaleY(0.5);
	transform: scaleY(0.5);
	bottom: 0;
	right: 0;
	left: 30rpx;
}

.tui-last::after {
	border-bottom: 0 !important;
}

.tui-btn__opentype {
	background: transparent !important;
	position: absolute;
	height: 100%;
	width: 100%;
	left: 0;
	top: 0;
	border: 0;
	z-index: 1;
}

.tui-btn__opentype::after {
	border: 0;
}

.tui-title--text {
	width: 100%;
	font-size: 28rpx;
	color: #888;
	padding: 30rpx;
	box-sizing: border-box;
}








/* 公共样式 */
body{word-wrap:break-word;word-break: break-all;} /*英文长单词换行*/

.color-parent{color: inherit!important;}
.color-blue{color: #5677FC!important;}
.color-blue2{color: #3a8ee6!important;}
.color-bluegrey{color: #C4DAF9!important;}
.color-bluegrey2{color: #F3F7FA!important;}
.color-red{color: #DA1D25!important;}
.color-green{color: #09BB07!important;}
.color-yellow{color: #FBECC1!important;}
.color-yellow2{color: #F49E49!important;}
.color-grey{color: #D0D2D8!important;}
.color-grey2{color: #9398A5!important;}
.color-grey3{color: #576B95!important;}
.color-999{color: #999!important;}
.color-bbb{color: #bbb!important;}
.color-ddd{color: #ddd!important;}
.color-fff{color: #fff!important;}

.bg-none{background: none!important;}
.bg-color-body{background-color: #F3F7FA!important;}
.bg-color-blue{background-color: #5677FC!important;}
.bg-color-blue2{background-color: #3a8ee6!important;}
.bg-color-bluegrey{background-color: #C4DAF9!important;}
.bg-color-bluegrey2{background-color: #F3F7FA!important;}
.bg-color-red{background-color: #DA1D25!important;}
.bg-color-grey{background-color: #D0D2D8!important;}
.bg-color-ddd{background-color: #ddd!important;}
.bg-color-fff{background-color: #fff!important;}
.bg-color-000s5{background-color: rgba(0,0,0,.5)!important;}
.bg-color-000s7{background-color: rgba(0,0,0,.7)!important;}

.clear{clear:both;}
.clearfix{zoom:1;}
.clearfix:after{visibility:hidden;display:block;content:" ";clear:both;}
.hover{cursor: pointer;user-select: none;}
.float-left{float: left;}
.float-right{float: right;}

.box-all{-moz-box-sizing:border-box;-webkit-box-sizing:border-box;box-sizing:border-box;}

.none{display: none;}
.hide{visibility: hidden;}
.visib{visibility: visible;}
.box-sizing{box-sizing:border-box;}
.block{display: block;}
.inblock{display: inline-block;vertical-align: top;}
.inblock-middle{display: inline-block;vertical-align: middle;}
.text-left{text-align: left;}
.text-center{text-align: center;}
.text-right{text-align: right;}
.text-both{text-align: justify;}

.flex{display: flex;}
.inflex{display: inline-flex;}
.flex-row{flex-direction: row;} /* 行从左到右排序(默认) */
.flex-row-r{flex-direction: row-reverse;} /* 行从右到左排序 */
.flex-col{flex-direction: column;} /* 列从上到下排序 */
.flex-col-b{flex-direction: column-reverse;} /* 列从下到上排序 */
.flex-wrap{flex-wrap: wrap;}
.flex-left{justify-content:flex-start;}
.flex-center{justify-content:center;}
.flex-right{justify-content:flex-end;}
.flex-even{justify-content:space-evenly;}
.flex-around{justify-content:space-around;}
.flex-both{justify-content:space-between;}
.flex-top{align-items: flex-start;}
.flex-middle{align-items: center;}
.flex-bottom{align-items: flex-end;}

.flex-item-top{align-self: flex-start;}
.flex-item-middle{align-self: center;}
.flex-item-bottom{align-self: flex-end;}
.flex-item-grow-1{flex-grow: 1;} /* 存在剩余空间将放大 */
.flex-item-shrink-0{flex-shrink: 0;} /* 空间不足将不缩小 */
.flex-item-order-0{order: 0;} /* 单独排序(默认:0) */
.flex-item-order-1{order: 1;} /* 单独排序 */
.flex-item-order-2{order: 2;} /* 单独排序 */

.grid{display: grid;}
.ingrid{display: inline-grid;}
.grid-row{grid-auto-flow: row;} /* 先行后列排序(默认) */
.grid-col{grid-auto-flow: column;} /* 先列后行排序 */
.grid-temp-col-12{grid-template-columns: repeat(12,1fr);}
.grid-temp-row-12{grid-template-rows: repeat(12,1fr);}
/* (项目上)网格单个项目-容纳几个网格 */
.grid-span-1{grid-column-start: span 1;}
.grid-span-2{grid-column-start: span 2;}
.grid-span-3{grid-column-start: span 3;}
.grid-span-4{grid-column-start: span 4;}
.grid-span-5{grid-column-start: span 5;}
.grid-span-6{grid-column-start: span 6;}
.grid-span-7{grid-column-start: span 7;}
.grid-span-8{grid-column-start: span 8;}
.grid-span-9{grid-column-start: span 9;}
.grid-span-10{grid-column-start: span 10;}
.grid-span-11{grid-column-start: span 11;}
.grid-span-12{grid-column-start: span 12;}
.grid-span-1-h{grid-row-start: span 1;}
.grid-span-2-h{grid-row-start: span 2;}
.grid-span-3-h{grid-row-start: span 3;}
.grid-span-4-h{grid-row-start: span 4;}
.grid-span-5-h{grid-row-start: span 5;}
.grid-span-6-h{grid-row-start: span 6;}
.grid-span-7-h{grid-row-start: span 7;}
.grid-span-8-h{grid-row-start: span 8;}
.grid-span-9-h{grid-row-start: span 9;}
.grid-span-10-h{grid-row-start: span 10;}
.grid-span-11-h{grid-row-start: span 11;}
.grid-span-12-h{grid-row-start: span 12;}
/* (容器上)网格项目-行列间距 */
.grid-gap-5{grid-gap: 0.05rem;}
.grid-gap-10{grid-gap: 0.10rem;}
.grid-gap-0_5{grid-gap: 0 0.05rem;}
.grid-gap-0_10{grid-gap: 0 0.10rem;}
.grid-gap-5_0{grid-gap: 0.05rem 0;}
.grid-gap-10_0{grid-gap: 0.10rem 0;}
.grid-gap-row-5{grid-row-gap: 0.05rem;}
.grid-gap-row-10{grid-row-gap: 0.10rem;}
.grid-gap-col-5{grid-column-gap: 0.05rem;}
.grid-gap-col-10{grid-column-gap: 0.10rem;}
/* (容器上)网格项目在整个的网格中-水平垂直对齐方式 */
.grid-center-center{place-content:center center;} /* 垂直和水平的简写(垂直+水平) */
.grid-left{justify-content:start;}
.grid-center{justify-content:center;}
.grid-right{justify-content:end;}
.grid-even{justify-content:space-evenly;}
.grid-around{justify-content:space-around;}
.grid-both{justify-content:space-between;}
.grid-just{justify-content:stretch;}
.grid-top{align-content:start;}
.grid-middle{align-content:center;}
.grid-bottom{align-content:end;}
.grid-even-h{align-content:space-evenly;}
.grid-around-h{align-content:space-around;}
.grid-both-h{align-content:space-between;}
.grid-full{align-content:stretch;}
/* (容器上)网格项目在各自的网格中-水平垂直对齐方式 */
.grid-items-center-center{place-items:center center;} /* 垂直和水平的简写(垂直+水平) */
.grid-items-left{justify-items:start;}
.grid-items-center{justify-items:center;}
.grid-items-right{justify-items:end;}
.grid-items-both{justify-items:stretch;}
.grid-items-top{align-items:start;}
.grid-items-middle{align-items:center;}
.grid-items-bottom{align-items:end;}
.grid-items-full{align-items:stretch;}
/* (项目上)网格单个项目在网格中-水平垂直对齐方式 */
.grid-item-right{justify-self:end;}
.grid-item-bottom{align-self:end;}

/* 网格边框 */
.grid-table{border: 1px solid #ddd;}
.grid-table>*{border: 1px solid #ddd;display: flex;align-items: center;padding: 0.10rem;}

.over-auto{overflow: auto;}
.over-auto-x{overflow-x: auto;}
.over-auto-y{overflow-y: auto;}
.over-hide{overflow: hidden;}
.over-hide-0{overflow: hidden;white-space: nowrap;text-overflow: ellipsis;}
.over-hide-1{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 1;-webkit-box-orient: vertical;}
.over-hide-2{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;}
.over-hide-3{overflow: hidden;display: -webkit-box;-webkit-line-clamp: 3;-webkit-box-orient: vertical;}

.posit-relat{position: relative;}
.posit-absol{position: absolute;}
.posit-fixed{position: fixed;}
.z-index--1{z-index: -1;}
.z-index-1{z-index: 1;}
.z-index-10{z-index: 10;}
.z-index-100{z-index: 100;}
.z-index-1000{z-index: 1000;}
.left-0{left: 0;}
.right-0{right: 0;}
.top-0{top: 0;}
.bottom-0{bottom: 0;}

.border-parent{border: 1px solid;}
.border-0{border: 0!important;}
.border-blue{border: 1px solid #5677FC!important;}
.border-bluegrey2{border: 1px solid #F3F7FA!important;}
.border-ddd{border: 1px solid #ddd!important;}
.border-t-ddd{border-top: 1px solid #ddd!important;}
.border-b-ddd{border-bottom: 1px solid #ddd!important;}
.border-b-bluegrey3{border-bottom: 1px solid #F4F6F8!important;}
.border-radius-3{border-radius: 6rpx;}
.border-radius-5{border-radius: 10rpx;}
.border-radius-8{border-radius: 16rpx;}
.border-radius-10{border-radius: 20rpx;}
.border-radius-15{border-radius: 30rpx;}
.border-radius-50{border-radius: 100rpx;}

.border-radius-t-15{border-top-left-radius: 30rpx;border-top-right-radius: 30rpx;}

.width-all{width: 100%;}
.height-all{height: 100%;}
.min-height-36{min-height: 72rpx;}

.line-none{line-height: normal;}
.line-1{line-height: 1;}
.line-1s2{line-height: 1.2;}
.line-1s4{line-height: 1.4;}
.line-1s6{line-height: 1.6;}
.line-2{line-height: 2;}


.font-none{font-weight: normal;}
.font-bold{font-weight: bold;}
.font-0{font-size: 0;}
.font-10{font-size: 20rpx;}
.font-11{font-size: 22rpx;}
.font-12{font-size: 24rpx;}
.font-13{font-size: 26rpx;}
.font-14{font-size: 28rpx;}
.font-15{font-size: 30rpx;}
.font-16{font-size: 32rpx;}
.font-18{font-size: 36rpx;}
.font-20{font-size: 40rpx;}
.font-22{font-size: 44rpx;}
.font-24{font-size: 48rpx;}
.font-26{font-size: 52rpx;}
.font-28{font-size: 56rpx;}
.font-30{font-size: 60rpx;}
.font-32{font-size: 64rpx;}
.font-34{font-size: 68rpx;}
.font-36{font-size: 72rpx;}

.padd-5{padding: 10rpx;}
.padd-10{padding: 20rpx;}
.padd-15{padding: 30rpx;}
.padd-20{padding: 40rpx;}
.padd-30{padding: 60rpx;}

.padd-5_10{padding: 10rpx 20rpx;}
.padd-10_20{padding: 20rpx 40rpx;}

.padd-0_8{padding: 0 16rpx;}
.padd-0_5{padding: 0 10rpx;}
.padd-0_10{padding: 0 20rpx;}
.padd-0_15{padding: 0 30rpx;}
.padd-0_20{padding: 0 40rpx;}

.padd-5_0{padding: 10rpx 0;}
.padd-10_0{padding: 20rpx 0;}
.padd-15_0{padding: 30rpx 0;}
.padd-20_0{padding: 40rpx 0;}
.padd-30_0{padding: 60rpx 0;}

.padd-t-0{padding-top: 0;}
.padd-t-5{padding-top: 10rpx;}
.padd-t-10{padding-top: 20rpx;}
.padd-t-20{padding-top: 40rpx;}
.padd-t-30{padding-top: 60rpx;}
.padd-b-0{padding-bottom: 0;}
.padd-b-5{padding-bottom: 10rpx;}
.padd-b-10{padding-bottom: 20rpx;}
.padd-b-20{padding-bottom: 40rpx;}
.padd-b-30{padding-bottom: 60rpx;}
.padd-l-5{padding-left: 10rpx;}
.padd-l-10{padding-left: 20rpx;}
.padd-l-15{padding-left: 30rpx;}
.padd-l-20{padding-left: 40rpx;}
.padd-r-5{padding-right: 10rpx;}
.padd-r-10{padding-right: 20rpx;}
.padd-r-15{padding-right: 30rpx;}
.padd-r-20{padding-right: 40rpx;}

.marg-auto{margin: auto;}
.marg-5{margin: 10rpx;}
.marg-10{margin: 20rpx;}
.marg-20{margin: 40rpx;}
.marg-30{margin: 60rpx;}

.marg-5_10{margin: 10rpx 20rpx;}
.marg-10_20{margin: 20rpx 40rpx;}

.marg-0_5{margin: 0 10rpx;}
.marg-0_10{margin: 0 20rpx;}
.marg-0_20{margin: 0 40rpx;}
.marg-5_0{margin: 10rpx 0;}
.marg-10_0{margin: 20rpx 0;}
.marg-20_0{margin: 40rpx 0;}

.marg-t-5{margin-top: 10rpx;}
.marg-t-10{margin-top: 20rpx;}
.marg-t-15{margin-top: 30rpx;}
.marg-t-20{margin-top: 40rpx;}
.marg-b-5{margin-bottom: 10rpx;}
.marg-b-10{margin-bottom: 20rpx;}
.marg-b-15{margin-bottom: 30rpx;}
.marg-b-20{margin-bottom: 40rpx;}
.marg-l-5{margin-left: 10rpx;}
.marg-l-10{margin-left: 20rpx;}
.marg-l-15{margin-left: 30rpx;}
.marg-l-20{margin-left: 40rpx;}
.marg-r-5{margin-right: 10rpx;}
.marg-r-10{margin-right: 20rpx;}
.marg-r-15{margin-right: 30rpx;}
.marg-r-20{margin-right: 40rpx;}

.box-shadow{box-shadow: 1px 2px 8px 0 #999;}
.box-shadow-filter{filter: drop-shadow(1px 2px 8px #999);}

/* 暂无数据 */
.no-data{
	text-align: center;
	font-weight: bold;
	font-size: 32rpx;
	padding: 60rpx 0;
}
/* 没有更多了 */
.no-more{
	text-align: center;
	color: #bbb;
	font-size: 28rpx;
}

.last-no-border:last-child{
	border-bottom: 0!important;
}

