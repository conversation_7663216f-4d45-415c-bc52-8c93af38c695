<template>
	<view class="container height-all flex flex-col">
		<tui-tab :tabs="tabs" :current="tabsKey" isFixed @change="fun_tabsChange" class="border-b-ddd"></tui-tab>
		<tui-white-space height="80"></tui-white-space>
		
		<!-- 列表菜单 -->
		<view class="font-14 flex-item-grow-1" style="min-height:0;height: 100%;">
			<mescroll-uni :fixed="false" ref="mescrollRef" @init="mescrollInit" @down="downCallback" @up="upCallback" :up="upOption" :down="downOption">
				<view class="bg-color-fff marg-t-10 padd-0_20" v-for="(item,key) of dataList" :key="key">
					<view class="padd-10_0" @click="fun_goInfo(item.planId)">
						<view class=" border-b-ddd padd-b-5">任务名称：{{item.inspectionPlanName}}</view>
						<view class="padd-t-5">
							<view class="flex flex-both">
								<view class="flex">
									<text class="flex-item-shrink-0">巡检项：</text>
									<text>{{item.inspectionItemName}}</text>
								</view>
								<view class="flex-item-shrink-0" style="margin-right: -40rpx;">
									<tui-tag :type="tagType[Number(item.taskCompleteState)]" shape="circleLeft" padding="6rpx 12rpx">{{rwztOption.find((v) =>Number(v.value) === Number(item.taskCompleteState))?.text}}</tui-tag>
								</view>
							</view>
							<!-- <view>任务编号：{{item.planId}}</view> -->
							<view>开始时间：{{item.startTime}}</view>
							<view>结束时间：{{item.endTime}}</view>
							<view class="flex">
								<text class="flex-item-shrink-0">巡检人员：</text>
								<text>{{item.inspectionPersonnel}}</text>
							</view>
						</view>
					</view>
				</view>
			</mescroll-uni>
		</view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';

	import MescrollMixin from "@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js";
	export default {
		mixins: [MescrollMixin],
		data() {
			return {
				isAdmin: this.tui.getUserInfo('isAdmin'),
				userId: this.tui.getUserInfo('userId'),
				tabsKey: 0,
				tabsId:'',
				tabs: [
					{name: "全部",id: ''},
					{name: "待巡检",id: '1'},
					{name: "已完成",id: '3'},
				],
				dataList: [
					// {itemName:'巡检项名称巡检项名称巡检项名称巡检项名称巡检项名称',taskName:'任务名称',taskCode:'任务编号',startTime:'2025-03-05 10:10:10',endTime:'2025-03-05 10:10:10',person:'人员名称'},
					// {itemName:'巡检项名称',taskName:'任务名称',taskCode:'任务编号',startTime:'2025-03-05 10:10:10',endTime:'2025-03-05 10:10:10',person:'人员名称'},
				],
				upOption:{
					auto: false, // 是否在初始化完毕之后自动执行上拉加载的回调; 默认true(下拉加载的配置中的自动执行也默认true)
					// page: {
					//     num: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始
					//     size: 10, // 每页数据的数量
					//     time: null // 加载第一页数据服务器返回的时间; 防止用户翻页时,后台新增了数据从而导致下一页数据重复;
					// },
					toTop:{
						src:''
					},
					textNoMore: '-- 没有更多了 --', // 没有更多数据的提示文本
					empty: {
						// icon: "", // 图标路径
						tip: '暂无相关数据'
					}
				},
				downOption:{
					auto: false, // 是否在初始化完毕之后自动执行下拉刷新的回调; 默认true
				},

				rwztOption:[
					{text:'待巡检',value:'1'},
					{text:'已完成',value:'3'},
				],//任务状态
				tagType: {
					0: 'primary',
					1: 'warning',
					2: 'danger',
					3: 'green',
				},
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.tabsKey) {
				this.tabsKey = e.tabsKey;
				this.tabsId = this.tabs[e.tabsKey]?.id;
			}
			// this.fun_getDicts();//获取字典
		},
		onShow() {
			this.$nextTick(() => {
				this.dataList = []; // 先置空列表,显示加载进度
				this.mescroll.resetUpScroll(); // 再刷新列表数据(默认会重置page.num=1)
			});
		},
		methods: {
			...mapActions(['getOnlineStatus']),
			fun_tabsChange(v) {
				console.log('fun_tabsChange',v.index,v.item.id);
				this.tabsKey = v.index;
				this.tabsId = v.item.id;
				this.dataList = [];
				this.mescroll.resetUpScroll();
			},
            //获取字典
            fun_getDicts() {
            	// this.http.get(this.$apiUrl.getDict, {
                //     data:{type:"JHRWZT"}
                // }).then( ({data}) => {
            	// 	console.log('任务状态:',data)
                //     this.rwztOption = data.list;
            	// })
            },
			/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */
			upCallback(page) {
				setTimeout(()=>{
				console.log('upCallback')
				//联网加载数据
				// apiGoods(page.num, page.size).then( ({data})=>{
				let params = {
					taskCompleteState: this.tabsId,
				};
				if(!this.isAdmin){
					params.inspectionPersonnelId = this.userId;
				}
				this.http.post(this.$apiUrl.inspectList, { data:{pageNum:page.num, pageSize:page.size,...params} }).then( ({object}) => {
					//方法二(推荐): 后台接口有返回列表的总数据量 totalSize
					this.mescroll.endBySize(object.records.length, object.total); //必传参数(当前页的数据个数, 总数据量)
					//方法四 (不推荐),会存在一个小问题:比如列表共有20条数据,每页加载10条,共2页.如果只根据当前页的数据个数判断,则需翻到第三页才会知道无更多数据
					// this.mescroll.endSuccess(object.list.length);

					//设置列表数据
					if(page.num == 1) this.dataList = []; //如果是第一页需手动制空列表
					this.dataList=this.dataList.concat(object.records); //追加新数据
				}).catch((err)=>{
					console.log('upCallback_err',err)
					//联网失败, 结束加载
					this.mescroll.endErr();
				})
				},300)
			},
			fun_goInfo(id) {
				uni.navigateTo({
					url: `/pages/inspect-manage/look?gid=${id}`
				});
			},
		},
	};
</script>

<style lang="scss">
	// page{background-color: #fff;}
	uni-page-body{height: 100%;}
</style>