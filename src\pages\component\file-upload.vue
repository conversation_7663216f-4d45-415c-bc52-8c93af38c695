<template>
	<view class="">
		<tui-upload ref="uploadRef" :limit="limit" :imageFormat="['jpg','png','jpeg']" :value="imageUploadValueArr" serverUrl="" :header="uploadHeader" :delTrigger="false" @complete="fun_imgComplete" @remove="fun_imgRemove" @reupload="fun_imgReUpload"></tui-upload>

	</view>
</template>

<script>
	export default {
		props: {
			//限制上传图片的数量
			limit: {
				type: Number,
				default: 9
			},

			//需要回显时的图片列表数据:{url:'必填',fileGid:'必填',gid:'选填'}(url是回显的图片地址;fileGid就是gid,fileGid是接口提交时用的字段,gid是上传后接口返回的字段)
			showFileList: {
				type: Array,
				default: () => {
					return []
				}
			},
		},
		data() {
			return {
				serverUrl: this.$apiUrl.uploadFile,//上传接口地址
				uploadHeader: {
					'Authorization': 'Bearer ' + this.tui.getToken(),
				},
				fileList:[],
				imageUploadValueArr: [],
				imageUploading_key: 0,
				imageUploading_total: 0,
				imageUploading: false,
			};
		},
		methods: {
			//图片上传函数
			fun_imageUpload(file) {
				// console.log('组件内返回的文件信息:', file);
				return new Promise((resolve, reject) => {
					// console.log('imageUploading_key',this.imageUploading_key);
					if(this.fileList.length>0&&this.fileList[this.imageUploading_key]){
						resolve(this.fileList[this.imageUploading_key].url);
					}else{
						this.tui.uploadFile(this.$apiUrl.uploadFile,file.path).then(res => {
							this.fileList.push({...res,fileGid:res.gid});
							this.imageUploadValueArr.push(res.url);
							this.$emit('fun_imageUploadChanged',this.fileList);
							console.log('上传后',this.fileList);
							resolve(res.url);
						}).catch(err => {
							reject(err);
						})
					}
					this.imageUploading_key++;
					if(this.imageUploading_key == this.imageUploading_total){
						this.imageUploading_key=0;
					}
				})
			},
			//图片上传完成
			fun_imgComplete(e) {
				//这个破组件每次点加号上传,在这里都会把所有的图片带上,不是只传当前选择的,并且有几张图片这里就会走几何式次数(真是服了,这大bug),而且服务地址为空会先走一遍,然后上传成功后会再走一遍,所以才会有如下代码
				if(this.imageUploading)return;
				this.imageUploading = true;
				setTimeout(() => {
					this.imageUploading = false;
				}, 500);
				console.log('imgComplete', e);
				//这个破组件每次点加号上传,在这里都会把所有的图片带上,不是只传当前选择的
				this.imageUploading_key=0;
				this.imageUploading_total=e.imgArr.length;
				this.$refs.uploadRef && this.$refs.uploadRef.upload(this.fun_imageUpload);
			},
			//图片删除
			fun_imgRemove(e) {
				console.log('imgRemove', e);
				this.fileList.splice(e.index, 1);
				this.imageUploadValueArr.splice(e.index, 1);
				this.$emit('fun_imageUploadChanged',this.fileList);
				console.log('删除后',this.fileList);
			},
			//重新上传
			fun_imgReUpload(e) {
				//当前上传按钮索引值
				console.log('重新上传-按钮索引值:',e.index)
				this.imageUploading_key = e.index;
				//调用upload 方法上传，并传入函数，且此时需要传入index值
				this.$refs.uploadRef && this.$refs.uploadRef.upload(this.fun_imageUpload, e.index)
			},
		},
	};
</script>

<style lang="scss">
    // page{background-color: #fff;}
</style>