import {ApiSite,ApiPrefix_oauth,ApiPrefix_ts,ApiPrefix_app,ApiPrefix_sys} from './serverConfig.js'



const apiUrl = {
    site: ApiSite,//接口地址
    //登录
    jlt_Login: ApiPrefix_oauth + '/sys/login',
    //获取用户信息
    // getUserInfo: ApiPrefix_ts + '/mobile/user/getLoginUserInfo',
    getUserInfo: ApiPrefix_sys + '/user/getInfo',
    //修改密码
    updatePassword: ApiPrefix_ts + '/system/user/profile/updatePwd',


    // 公共系统字典表(有缓存):get,直接拼接值
    getSystemDict: ApiPrefix_app + '/system/dict/data/type/',//get,直接拼接值
    // 公共业务字典表(有缓存):get
    getDict: ApiPrefix_app + '/system/common/getDict',
    // 公共业务字典表(没有缓存):get,直接拼接值
    getBusinessDict: ApiPrefix_app + '/system/business/data/type/',//get,直接拼接值
    
    //上传文件
    uploadFile: ApiSite+ApiPrefix_app + '/files/upload',

    //获取用户列表
    getUserList: ApiPrefix_sys + '/user/findUserList',


    //获取设备列表
    // getDeviceAllList: ApiPrefix_app + '/equipmentInfo/getAllList',
    //获取设备详情
    getDeviceDetail: ApiPrefix_app + '/tMaintain/getEquiInfo',
    //获取设备所属热力站信息(参数:keyId)
    getDeviceOfHeatStation: ApiPrefix_ts + '/equipmentUnitDevice/getInfo',
    //获取设备所属小区信息(参数:id)
    getDeviceOfCommunity: ApiPrefix_ts + '/tsEquipmentInfo/getInfo',


    //巡检任务列表
	inspectList: ApiPrefix_app + '/tMeInspectionplan/byPage',
    //巡检任务详情
    inspectDetail: ApiPrefix_app + '/tMeInspectionplan/getInfo',
    //巡检任务详情设备列表
    inspectDetailDeviceList: ApiPrefix_app + '/tPlanDetail/byPage',
    //巡检任务详情单个设备上报
    inspectDetailOneDeviceSubmit: ApiPrefix_app + '/tPlanDetail/editTPlanDetail',
    //巡检任务详情结束提交(所有设备列表提交)
    // inspectDetailDeviceSubmit: ApiPrefix_app + '/tMeInspectionplan/completeTMeInspectionplan',
    inspectDetailSubmit: ApiPrefix_app + '/tMeInspectionplan/updateTMeInspectionplan',
    
    //待维修列表
    maintList: ApiPrefix_app + '/tMaintenance/byPage',
    //热力站选项列表
    heatStationOptions: ApiPrefix_ts + '/station/selectAllList',
    //热力站机组选项列表
    heatStationUnitOptions: ApiPrefix_ts + '/equipmentUnit/getAllListByParam',
    //获取热力站设备选项列表
    heatStationDeviceOptions: ApiPrefix_ts + '/equipmentUnitDevice/byPage',

    //获取小区选项列表
    getCommunityOptions: ApiPrefix_ts + '/community/getAllListByParam',
    //获取楼栋选项列表
    getBuildingOptions: ApiPrefix_ts + '/building/getAllListByParam',
    //获取单元选项列表
    getUnitOptions: ApiPrefix_ts + '/buildingUnit/getAllListByParam',
    //获取缴费户选项列表
    getHouseholdOptions: ApiPrefix_ts + '/heatUser/getAllListByParam',
    //获取设备选项列表
    getDeviceOptions: ApiPrefix_ts + '/tsEquipmentInfo/byPage',

    //维修添加
    maintAdd: ApiPrefix_app + '/tMaintenance/addSelfTMaintenance',
    //维修详情
    maintDetail: ApiPrefix_app + '/tMaintenance/getInfo',
    //维修代办-流程上报及隐患的维修派发和办结
    maintProcessAdd: ApiPrefix_app + '/tMaintenanceStep/addTMaintenanceStep',
    //维修-隐患上报及报警的维修派发和办结
    maintPitfallAdd: ApiPrefix_app + '/tMaintenance/addTMaintenance',

    //报警列表
    alarmList: ApiPrefix_ts + '/heatWarningRecord/byPage',
    //报警确认
    alarmConfirm: ApiPrefix_ts + '/heatWarningRecord/batchUpdateState',
    //报警详情
    alarmDetail: ApiPrefix_ts + '/heatWarningRecord/getInfoAndMaintenance',


    //消息列表
    messageList: ApiPrefix_app + '/msg/byPage',
    //消息已读
    messageRead: ApiPrefix_app + '/msg',



    //公共字典
    localCommonDict: {
        //隐患类型
        dangerTypeOptions: [
            {text:'脱节断裂',value:'00304'},
            {text:'管网脱节断裂',value:'00405'},
            {text:'管网淤积',value:'00402'},
            {text:'地基层降',value:'01002'},
            {text:'裂缝',value:'01003'},
            {text:'管网泄露',value:'00104'},
            {text:'违章占压',value:'00102'},
            {text:'管网爆管',value:'00301'},
            {text:'交叉穿越',value:'00101'},
            {text:'管网老化腐蚀',value:'00103'},
            {text:'地面沉降',value:'00303'},
            {text:'管网老旧腐蚀',value:'00302'},
            {text:'暴雨洪涝',value:'00401'},
            {text:'管网溢流',value:'00403'},
            {text:'管网腐蚀',value:'00404'},
            {text:'结构损伤',value:'01001'},
            {text:'其他',value:'01004'},
        ],
        //隐患状态
		dangerStatusOptions:[
			{text:'上报-待派发',value:'0'},
			{text:'派发-待处置',value:'1'},
			{text:'处置完成-待办结',value:'2'},
			{text:'退回',value:'3'},
			{text:'办结',value:'4'},
		],
        //隐患等级
        dangerLevelOptions:[
            {text:'低隐患',value:'1'},
            {text:'一般隐患',value:'2'},
            {text:'较大隐患',value:'3'},
            {text:'重大隐患',value:'4'},
        ],
        //报警状态
        alarmStatusOptions:[
            {text:'报警中',value:'0'},
            {text:'已恢复',value:'1'},
        ],
        // 报警处置状态
		alarmHandleStatusOptions:[
			{text:'待研判',value:'1'},
			{text:'待派发',value:'2'},
			{text:'待核查',value:'3'},
			{text:'待处置',value:'4'},
			{text:'待办结',value:'5'},
			{text:'已办结',value:'6'},
			{text:'核查退回',value:'7'},
			{text:'处置退回',value:'8'},
			{text:'系统误报',value:'9'},
		],
        //报警等级
        alarmLevelOptions:[
            {text:'预警',value:'-1'},
            {text:'低级',value:'0'},
            {text:'中级',value:'1'},
            {text:'紧急',value:'2'},
        ],
        //小区设备类型(8只下到小区;1,3,7下到单元;2,4,5,6下到户)
        communityDeviceTypeOptions: [
            {text:'单元阀',value:'1'},
            {text:'户阀',value:'2'},
            {text:'单元热表',value:'3'},
            {text:'户用热表',value:'4'},
            {text:'室温设备',value:'5'},
            {text:'墙温设备',value:'6'},
            {text:'楼温设备',value:'7'},
            {text:'井室设备',value:'8'},
        ],
        //热力站设备类型
        heatStationDeviceTypeOptions: [
            {text:'循环泵',value:'1'},
            {text:'补水泵',value:'2'},
            {text:'加压泵',value:'3'},
            {text:'调节阀',value:'4'},
            {text:'水箱进水阀',value:'5'},
            {text:'一补二阀',value:'6'},
            {text:'泄压阀',value:'7'},
            {text:'水箱',value:'8'},
            {text:'换热器',value:'9'},
            {text:'温变',value:'10'},
            {text:'压变',value:'11'},
            {text:'热表',value:'12'},
            {text:'水表',value:'13'},
            {text:'电表',value:'14'},
        ],


    },

}

export default apiUrl
