<template>
	<view class="padd-t-5">
		<tui-form :tip-top="0" ref="form" :show-message="false" :model="formData">
			<tui-form-item label="派发" prop="personId" asterisk arrow v-if="type=='maint'">
				<tui-input v-model="formData.personName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="personOptionsShow = true"></tui-input>
				<tui-picker :pickerData="personOptions" :show="personOptionsShow" @hide="personOptionsShow = false" @change="fun_personOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="办结" prop="radioType" asterisk v-else-if="type=='end'">
				<tui-radio-group v-model="formData.radioType">
					<tui-label class="inblock" style="margin-right:40rpx;">
						<tui-radio value="4" color="#07c160" borderColor="#999">
						</tui-radio>
						<text class="padd-l-5">办结</text>
					</tui-label>
					<tui-label class="inblock" style="margin-right:40rpx;">
						<tui-radio value="3" color="#f8683c" borderColor="#999">
						</tui-radio>
						<text class="padd-l-5">退回</text>
					</tui-label>
				</tui-radio-group>
			</tui-form-item>
			<tui-form-item label="说明" :position="1" :bottom-border="false">
				<template v-slot:row>
					<tui-textarea padding="0 30rpx 30rpx" :border-top="false" placeholder="请输入内容" is-counter v-model="formData.desc"></tui-textarea>
				</template>
			</tui-form-item>

			<view class="flex flex-center marg-t-20">
				<tui-button width="400rpx" height="84rpx" bold @click="submit">提交</tui-button>
			</view>
		</tui-form>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';
	
	import fileUpload from '@/pages/component/file-upload.vue';	//图片上传组件
	
	export default {
		components: {fileUpload},
		data() {
			return {
				gid: '',
				type: '',
				from: '',//来源:alarm报警,danger隐患
				deviceId: '',
				deviceName: '',

				formData: {
					personId: '',
					personName: '',

					radioType: '',
					
					desc: '',
				},
				rules: [
					// {name: "desc",rule: ["required"],msg: ["请输入说明"]},
				],
				rules_person: [
					{name: "personId",rule: ["required"],msg: ["请选择派发"]},
				],
				rules_radio: [
					{name: "radioType",rule: ["required"],msg: ["请选择办结"]},
				],

				personOptionsShow: false,
				personOptions: [],
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.gid){
				this.gid = e.gid;
				this.type = e.type;
				this.from = e.from;
				this.deviceId = e.deviceId;
				this.deviceName = e.deviceName;
				if(this.type=='maint'){
					this.fun_getUserList();
				}
			}
		},
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取用户列表
			fun_getUserList() {
				this.http.get(this.$apiUrl.getUserList,{
					data:{
						
					}
				}).then( ({object}) => {
					console.log('获取用户列表',object);
					this.personOptions = object.map(v=>{
						return {
							value: v.userId,
							// text: v.loginName,
							text: v.realName,
						}
					})
				})
			},
			//选择用户
			fun_personOptionsChange(e) {
				console.log('personOptionsChange', e);
				this.formData.personId = e.value;
				this.formData.personName = e.text;
			},
			
			
			//提交
			submit() {
				let formRules = [];
				formRules.push(...this.rules);
				if(this.type=='maint'){
					formRules.push(...this.rules_person);
				}else if(this.type=='end'){
					formRules.push(...this.rules_radio);
				}
				this.$refs.form.validate(this.formData, formRules, true).then(res => {
					if (res.isPass) {
						console.log(this.formData)
						let params={}
						let apiUrl = '';
						//隐患来的
						if(this.from=='danger'){
							params={
								...params,
								maintenanceId: this.gid,
								deviceId: this.deviceId,
								deviceName: this.deviceName,
								maintenanceMsg: this.formData.desc,
							}
							if(this.type=='maint'){
								//派发
								params = {
									...params,
									maintUserId: this.formData.personId,
									maintUserName: this.formData.personName,
									maintenanceStepType: "1",
									maintenanceStep: "派发-待处置",
								}
							}else if(this.type=='end'){
								//办结
								params = {
									...params,
									maintenanceStepType: this.formData.radioType,
									maintenanceStep: this.formData.radioType=="4"?"办结":"退回",
								}
							}
							apiUrl = this.$apiUrl.maintProcessAdd;
						}
						//报警来的
						else if(this.from=='alarm'){
							params = {
								...params,
								riskId: this.gid,
								sbbm: this.deviceId,
								sbmc: this.deviceName,
								descMsg: this.formData.desc,
								remark: "隐患",
							}
							if(this.type=='maint'){
								//派发
								params = {
									...params,
									maintUserId: this.formData.personId,
									maintUserName: this.formData.personName,
									bjczjddm: "1",
									bjczjdmc: "派发-待处置",
								}
							}else if(this.type=='end'){
								//办结
								params = {
									...params,
									bjczjddm: this.formData.radioType,
									bjczjdmc: this.formData.radioType=="4"?"办结":"退回",
								}
							}
							apiUrl = this.$apiUrl.maintPitfallAdd;
						}else{
							return;
						}

						this.http.post(apiUrl, {
							data:{
								...params,
							}
						}).then(res => {
							console.log(res);
							this.tui.toast("提交成功")
							setTimeout(() => {
								uni.navigateBack()
							}, 2000);
						})
					} else {
						console.log('校验不通过:',res)
					}
				}).catch(errors => {
					console.log('校验失败:',errors)
				})
			},
		},
	};
</script>

<style lang="scss">
    // page{background-color: #fff;}
</style>