<template>
	<view class="padd-t-5">
		<tui-form :tip-top="0" ref="form" :show-message="false" :model="formData">
			<tui-form-item label="设施" prop="facilitiesId" asterisk arrow>
				<tui-input v-model="formData.facilitiesName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(facilitiesOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="facilitiesOptions" :show="facilitiesOptionsShow" @hide="facilitiesOptionsShow = false" @change="fun_facilitiesOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="热力站" prop="heatStationId" asterisk arrow v-if="formData.facilitiesId == '0'">
				<tui-input v-model="formData.heatStationName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(heatStationOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="heatStationOptions" :show="heatStationOptionsShow" @hide="heatStationOptionsShow = false" @change="fun_heatStationOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="机组" prop="heatStationUnitId" asterisk arrow v-if="formData.facilitiesId == '0'">
				<tui-input v-model="formData.heatStationUnitName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(heatStationUnitOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="heatStationUnitOptions" :show="heatStationUnitOptionsShow" @hide="heatStationUnitOptionsShow = false" @change="fun_heatStationUnitOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="设备类型" prop="heatStationDeviceTypeId" asterisk arrow v-if="formData.facilitiesId == '0'">
				<tui-input v-model="formData.heatStationDeviceTypeName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(heatStationDeviceTypeOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="heatStationDeviceTypeOptions" :show="heatStationDeviceTypeOptionsShow" @hide="heatStationDeviceTypeOptionsShow = false" @change="fun_heatStationDeviceTypeOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="设备名称" prop="heatStationDeviceId" asterisk arrow v-if="formData.facilitiesId == '0'">
				<tui-input v-model="formData.heatStationDeviceName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(heatStationDeviceOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="heatStationDeviceOptions" :show="heatStationDeviceOptionsShow" @hide="heatStationDeviceOptionsShow = false" @change="fun_heatStationDeviceOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="设备类型" prop="deviceTypeId" asterisk arrow v-if="formData.facilitiesId == '1'">
				<tui-input v-model="formData.deviceTypeName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(deviceTypeOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="deviceTypeOptions" :show="deviceTypeOptionsShow" @hide="deviceTypeOptionsShow = false" @change="fun_deviceTypeOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="小区名称" prop="communityId" asterisk arrow v-if="formData.facilitiesId == '1'">
				<tui-input v-model="formData.communityName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(communityOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="communityOptions" :show="communityOptionsShow" @hide="communityOptionsShow = false" @change="fun_communityOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="楼栋名称" prop="buildingId" asterisk arrow v-if="formData.deviceTypeId&&formData.deviceTypeId != 8">
				<tui-input v-model="formData.buildingName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(buildingOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="buildingOptions" :show="buildingOptionsShow" @hide="buildingOptionsShow = false" @change="fun_buildingOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="单元名称" prop="unitId" asterisk arrow v-if="formData.deviceTypeId&&formData.deviceTypeId !=8 ">
				<tui-input v-model="formData.unitName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(unitOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="unitOptions" :show="unitOptionsShow" @hide="unitOptionsShow = false" @change="fun_unitOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="缴费户名" prop="householdId" asterisk arrow v-if="formData.deviceTypeId == 2||formData.deviceTypeId == 4||formData.deviceTypeId == 5||formData.deviceTypeId == 6">
				<tui-input v-model="formData.householdName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(householdOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="householdOptions" :show="householdOptionsShow" @hide="householdOptionsShow = false" @change="fun_householdOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="设备名称" prop="deviceId" asterisk arrow v-if="formData.facilitiesId == '1'">
				<tui-input v-model="formData.deviceName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="!inspectTaskDialogType&&(deviceOptionsShow = true)"></tui-input>
				<tui-picker :pickerData="deviceOptions" :show="deviceOptionsShow" @hide="deviceOptionsShow = false" @change="fun_deviceOptionsChange"></tui-picker>
				<!-- <tui-picker :pickerData="deviceOptions" :show="deviceOptionsShow" @hide="deviceOptionsShow = false" @change="fun_deviceOptionsChange"></tui-picker> -->
				<!-- <tui-bottom-popup :show="deviceOptionsShow" height="800" @close="deviceOptionsShow = false">
					<view class="padd-10 height-all box-all">
						<view class="height-all over-auto-y">
							<tui-checkbox-group v-model="formData.deviceId" @change="fun_deviceOptionsChange">
								<tui-label v-for="(item,index) in deviceOptions" :key="index">
									<tui-list-cell>
										<view class="thorui-align__center flex-center">
											<tui-checkbox :value="item.value" color="#f8683c" borderColor="#999" :checked="item.checked" :disabled="item.checked"></tui-checkbox>
											<text class="tui-text padd-l-5">{{item.text}}</text>
										</view>
									</tui-list-cell>
								</tui-label>
							</tui-checkbox-group>
						</view>
					</view>
				</tui-bottom-popup> -->
			</tui-form-item>
			<tui-form-item label="隐患名称" prop="danger" asterisk>
				<tui-input v-model="formData.danger" padding="0" :borderBottom="false" placeholder="请输入"></tui-input>
			</tui-form-item>
			<tui-form-item label="隐患类型" prop="dangerTypeId" asterisk arrow>
				<tui-input v-model="formData.dangerTypeName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="dangerTypeOptionsShow = true"></tui-input>
				<tui-picker :pickerData="dangerTypeOptions" :show="dangerTypeOptionsShow" @hide="dangerTypeOptionsShow = false" @change="fun_dangerTypeOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="隐患等级" prop="dangerLevelId" asterisk arrow>
				<tui-input v-model="formData.dangerLevelName" padding="0" :borderBottom="false" placeholder="请选择" disabled @click="dangerLevelOptionsShow = true"></tui-input>
				<tui-picker :pickerData="dangerLevelOptions" :show="dangerLevelOptionsShow" @hide="dangerLevelOptionsShow = false" @change="fun_dangerLevelOptionsChange"></tui-picker>
			</tui-form-item>
			<tui-form-item label="隐患描述" prop="desc" asterisk :position="1" :bottom-border="false">
				<template v-slot:row>
					<tui-textarea padding="0 30rpx 30rpx" :border-top="false" placeholder="请输入内容" is-counter v-model="formData.desc"></tui-textarea>
				</template>
			</tui-form-item>
			<tui-form-item label="维修前" prop="fileBeforeList" asterisk>
				<file-upload @fun_imageUploadChanged="fun_imageUploadChanged"></file-upload>
			</tui-form-item>
			<tui-form-item label="维修后" prop="fileEndList" asterisk v-if="!isFromDanger&&inspectTaskDialogType!='danger'">
				<file-upload @fun_imageUploadChanged="fun_imageUploadChanged2"></file-upload>
			</tui-form-item>

			<view class="flex flex-center marg-t-20">
				<tui-button width="400rpx" height="84rpx" bold @click="submit">提交</tui-button>
			</view>
		</tui-form>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';
	
	import fileUpload from '@/pages/component/file-upload.vue';	//图片上传组件
	
	export default {
		components: {fileUpload},
		data() {
			return {
				formData: {
					planId: '',

					facilitiesId: '',
					facilitiesName: '',

					heatStationId: '',
					heatStationName: '',
					heatStationUnitId: '',
					heatStationUnitName: '',
					heatStationDeviceTypeId: '',
					heatStationDeviceTypeName: '',
					heatStationDeviceId: '',
					heatStationDeviceName: '',
					
					deviceTypeId: '',
					deviceTypeName: '',
					communityId: '',
					communityName: '',
					buildingId: '',
					buildingName: '',
					unitId: '',
					unitName: '',
					householdId: '',
					householdName: '',
					deviceId: '',
					deviceName: '',

					danger: '',
					dangerTypeId: '',
					dangerTypeName: '',
					dangerLevelId: '',
					dangerLevelName: '',
					desc: '',
					fileBeforeList: [],
					fileEndList: [],
				},
				rules: [
					{name: "facilitiesId",rule: ["required"],msg: ["请选择设施"]},

					{name: "danger",rule: ["required"],msg: ["请输入隐患名称"]},
					{name: "dangerTypeId",rule: ["required"],msg: ["请选择隐患类型"]},
					{name: "dangerLevelId",rule: ["required"],msg: ["请选择隐患等级"]},
					// {name: "desc",rule: ["required"],msg: ["请输入隐患描述"]},
					{name: "fileBeforeList",rule: ["required"],msg: ["请上传图片"],
						validator: [{msg: "请上传图片",method: (value) => {
							return value.length > 0;
						}}]
					},
				],
				rules_maintFileEndList:[
					{name: "fileEndList",rule: ["required"],msg: ["请上传图片"],
						validator: [{msg: "请上传图片",method: (value) => {
							return value.length > 0;
						}}]
					},
				],
				rules_heatStation:[
					{name: "heatStationId",rule: ["required"],msg: ["请选择热力站"]},
					{name: "heatStationUnitId",rule: ["required"],msg: ["请选择机组"]},
					{name: "heatStationDeviceTypeId",rule: ["required"],msg: ["请选择设备类型"]},
					{name: "heatStationDeviceId",rule: ["required"],msg: ["请选择设备"]},
				],
				rules_device:[
					{name: "deviceTypeId",rule: ["required"],msg: ["请选择设备类型"]},
					// {name: "communityId",rule: ["required"],msg: ["请选择小区"]},
					// {name: "buildingId",rule: ["required"],msg: ["请选择楼栋"]},
					// {name: "unitId",rule: ["required"],msg: ["请选择单元"]},
					// {name: "householdId",rule: ["required"],msg: ["请选择户名"]},
					{name: "deviceId",rule: ["required"],msg: ["请选择设备"],
						// validator: [{msg: "请选择设备",method: (value) => {
						// 	return value.length > 0;
						// }}]
					},
				],
				rules_community:[
					{name: "communityId",rule: ["required"],msg: ["请选择小区"]},
				],
				rules_unit:[
					{name: "communityId",rule: ["required"],msg: ["请选择小区"]},
					{name: "buildingId",rule: ["required"],msg: ["请选择楼栋"]},
					{name: "unitId",rule: ["required"],msg: ["请选择单元"]},
				],
				rules_household:[
					{name: "communityId",rule: ["required"],msg: ["请选择小区"]},
					{name: "buildingId",rule: ["required"],msg: ["请选择楼栋"]},
					{name: "unitId",rule: ["required"],msg: ["请选择单元"]},
					{name: "householdId",rule: ["required"],msg: ["请选择户名"]},
				],

				facilitiesOptionsShow: false,
				facilitiesOptions: [
					{text:'热力站',value:'0'},
					{text:'设备',value:'1'},
				],

				heatStationOptionsShow: false,
				heatStationOptions: [],
				heatStationUnitOptionsShow: false,
				heatStationUnitOptions: [],
				heatStationDeviceTypeOptionsShow: false,
				heatStationDeviceTypeOptions: [],//热力站设备类型
				heatStationDeviceOptionsShow: false,
				heatStationDeviceOptions: [],

				deviceTypeOptionsShow: false,
				deviceTypeOptions: [],//小区设备类型(8只下到小区;1,3,7下到单元;2,4,5,6下到户)
				communityOptionsShow: false,
				communityOptions: [],
				buildingOptionsShow: false,
				buildingOptions: [],
				unitOptionsShow: false,
				unitOptions: [],
				householdOptionsShow: false,
				householdOptions: [],
				deviceOptionsShow: false,
				deviceOptions: [],

				dangerTypeOptionsShow: false,
				dangerTypeOptions: [],//隐患类型
				dangerLevelOptionsShow: false,
				dangerLevelOptions: [],//隐患等级

				//隐患页面跳转过来的
				isFromDanger:false,
				//巡检详情跳转过来的
				inspectTaskDialogType:'',
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			//巡检详情跳转过来的
			if(e.inspectTaskDialogType){
				if(e.inspectTaskDialogType=='danger'){
					uni.setNavigationBarTitle({
						title: '隐患上报'
					})
				}
				this.inspectTaskDialogType = e.inspectTaskDialogType;
				if(e.planId)this.formData.planId = e.planId;
				if(e.deviceId)this.formData.deviceId = e.deviceId;
				//获取设备所属信息
				this.fun_getOneDeviceOfArea();
			}
			//隐患页面跳转过来的
			if(e.isFromDanger){
				uni.setNavigationBarTitle({
					title: '隐患上报'
				})
				this.isFromDanger = e.isFromDanger;
			}

			this.fun_getDicts();//获取字典表数据
			// this.fun_getHeatStationOptions();//获取热力站选项列表
			// this.fun_getCommunityOptions();//获取小区列表
			// this.fun_getDeviceOptions();//获取设备列表

		},
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取字典表数据
			fun_getDicts() {
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHLX"}
				// }).then( ({data}) => {
				// 	console.log('隐患类型:',data)
				// 	this.dangerTypeOptions = data.list.map(v=>{
				// 		return {
				// 			value: v.businessValue,
				// 			text: v.businessLabel,
				// 		}
				// 	});
				// })
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHDJ"}
				// }).then( ({data}) => {
				// 	console.log('隐患等级:',data)
				// 	this.dangerLevelOptions = data.list.map(v=>{
				// 		return {
				// 			value: v.businessValue,
				// 			text: v.businessLabel,
				// 		}
				// 	});
				// })
				this.heatStationDeviceTypeOptions = this.$apiUrl.localCommonDict.heatStationDeviceTypeOptions;
				this.deviceTypeOptions = this.$apiUrl.localCommonDict.communityDeviceTypeOptions;
				this.dangerTypeOptions = this.$apiUrl.localCommonDict.dangerTypeOptions;
				this.dangerLevelOptions = this.$apiUrl.localCommonDict.dangerLevelOptions;
			},
			//获取设备所属信息
			fun_getOneDeviceOfArea() {
				if(this.formData.facilitiesId == '0'){
					//热力站
					this.http.get(this.$apiUrl.getDeviceOfHeatStation, {
						data: {
							keyId: this.formData.deviceId,
						}
					}).then( ({object}) => {
						console.log('获取热力站设备所属信息:',object);
						this.formData.facilitiesId = object.deviceFacilities+'';
						this.formData.facilitiesName = this.facilitiesOptions.find(v=>v.value==object.deviceFacilities)?.text;
						//
						this.formData.heatStationId = object.stationId;
						this.formData.heatStationName = object.stationName;
						this.formData.heatStationUnitId = object.equipmentUnitId;
						this.formData.heatStationUnitName = object.equipmentUnitName;
						this.formData.heatStationDeviceTypeId = object.equipmentType;
						this.formData.heatStationDeviceTypeName = object.equipmentTypeValue;

						this.formData.deviceName = object.equipmentName;
					})
				}else{
					//设备
					this.http.get(this.$apiUrl.getDeviceOfCommunity, {
						data: {
							id: this.formData.deviceId,
						}
					}).then( ({object}) => {
						console.log('获取小区设备所属信息:',object);
						this.formData.facilitiesId = object.deviceFacilities+'';
						this.formData.facilitiesName = this.facilitiesOptions.find(v=>v.value==object.deviceFacilities)?.text;
						//
						this.formData.deviceTypeId = object.equipmentType;
						this.formData.deviceTypeName = object.equipmentTypeValue;
						this.formData.communityId = object.communityId;
						this.formData.communityName = object.communityName;
						this.formData.buildingId = object.buildingId||'';
						this.formData.buildingName = object.buildingName||'';
						this.formData.unitId = object.buildingUnitId||'';
						this.formData.unitName = object.buildingUnitName||'';
						this.formData.householdId = object.heatUserId||'';
						this.formData.householdName = object.heatUserName||'';

						this.formData.deviceName = object.equipmentName;
					})
				}
			},
			//选择设施
			fun_facilitiesOptionsChange(e) {
				console.log('facilitiesOptionsChange', e);
				this.formData.facilitiesId = e.value;
				this.formData.facilitiesName = e.text;
				
				this.formData.heatStationId = '';
				this.formData.heatStationName = '';
				this.formData.heatStationUnitId = '';
				this.formData.heatStationUnitName = '';
				this.formData.heatStationDeviceTypeId = '';
				this.formData.heatStationDeviceTypeName = '';
				this.formData.heatStationDeviceId = '';
				this.formData.heatStationDeviceName = '';

				this.formData.deviceTypeId = '';
				this.formData.deviceTypeName = '';
				this.formData.communityId = '';
				this.formData.communityName = '';
				this.formData.buildingId = '';
				this.formData.buildingName = '';
				this.formData.unitId = '';
				this.formData.unitName = '';
				this.formData.householdId = '';
				this.formData.householdName = '';
				this.formData.deviceId = '';
				this.formData.deviceName = '';

				if(e.value == 0&&this.heatStationOptions.length == 0){
					this.fun_getHeatStationOptions();//获取热力站选项列表
				}else if(e.value == 1&&this.communityOptions.length == 0){
					this.fun_getCommunityOptions();//获取小区列表
				}
			},
			//获取热力站选项列表
			fun_getHeatStationOptions() {
				this.http.post(this.$apiUrl.heatStationOptions).then( ({object}) => {
					console.log('热力站选项列表',object);
					this.heatStationOptions = object.map(v=>{
						return {
							value: v.stationId,
							text: v.stationName,
						}
					})
				})
			},
			//选择热力站
			fun_heatStationOptionsChange(e) {
				console.log('heatStationOptionsChange', e);
				this.formData.heatStationId = e.value;
				this.formData.heatStationName = e.text;

				this.formData.heatStationUnitId = '';
				this.formData.heatStationUnitName = '';
				this.formData.heatStationDeviceTypeId = '';
				this.formData.heatStationDeviceTypeName = '';
				this.formData.heatStationDeviceId = '';
				this.formData.heatStationDeviceName = '';
				this.fun_getHeatStationUnitOptions();//获取热力站机组选项列表

			},
			//获取热力站机组选项列表
			fun_getHeatStationUnitOptions() {
				this.http.post(this.$apiUrl.heatStationUnitOptions,{
					data:{
						stationId: this.formData.heatStationId,
					}
				}).then( ({object}) => {
					console.log('热力站机组选项列表',object);
					this.heatStationUnitOptions = object.map(v=>{
						return {
							value: v.equipmentUnitId,
							text: v.equipmentUnitName,
						}
					})
				})
			},
			//选择热力站机组
			fun_heatStationUnitOptionsChange(e) {
				console.log('heatStationUnitOptionsChange', e);
				this.formData.heatStationUnitId = e.value;
				this.formData.heatStationUnitName = e.text;
				this.formData.heatStationDeviceTypeId = '';
				this.formData.heatStationDeviceTypeName = '';
				this.formData.heatStationDeviceId = '';
				this.formData.heatStationDeviceName = '';
			},
			//选择热力站设备类型
			fun_heatStationDeviceTypeOptionsChange(e) {
				console.log('heatStationDeviceTypeOptionsChange', e);
				this.formData.heatStationDeviceTypeId = e.value;
				this.formData.heatStationDeviceTypeName = e.text;
				this.formData.heatStationDeviceId = '';
				this.formData.heatStationDeviceName = '';
				this.fun_getHeatStationDeviceOptions();//获取热力站设备选项列表
			},
			//获取热力站设备选项列表
			fun_getHeatStationDeviceOptions() {
				this.http.post(this.$apiUrl.heatStationDeviceOptions,{
					data:{
						stationId: this.formData.heatStationId,
						equipmentUnitId: this.formData.heatStationUnitId,
						deviceType: this.formData.heatStationDeviceTypeId,
					}
				}).then( ({object}) => {
					console.log('热力站设备选项列表',object);
					this.heatStationDeviceOptions = object?.records?.map(v=>{
						return {
							value: v.equipmentUnitDeviceId,
							text: v.equipmentUnitDeviceName,
						}
					})
				})
			},
			//选择热力站设备
			fun_heatStationDeviceOptionsChange(e) {
				console.log('heatStationDeviceOptionsChange', e);
				this.formData.heatStationDeviceId = e.value;
				this.formData.heatStationDeviceName = e.text;
			},

			//选择设备类型
			fun_deviceTypeOptionsChange(e) {
				console.log('deviceTypeOptionsChange', e);
				this.formData.deviceTypeId = e.value;
				this.formData.deviceTypeName = e.text;

				if(e.value == 8){  //下到小区
					this.formData.buildingId = '';
					this.formData.buildingName = '';
					this.formData.unitId = '';
					this.formData.unitName = '';
					this.formData.householdId = '';
					this.formData.householdName = '';
					this.formData.deviceId = '';
					this.formData.deviceName = '';
				}else if(e.value == 2||e.value == 4||e.value == 5||e.value == 6){  //下到户
					this.formData.deviceId = '';
					this.formData.deviceName = '';
				}else{  //下到单元
					this.formData.householdId = '';
					this.formData.householdName = '';
					this.formData.deviceId = '';
					this.formData.deviceName = '';
				}
			},
			//获取小区列表
			fun_getCommunityOptions() {
				this.http.post(this.$apiUrl.getCommunityOptions).then( ({object}) => {
					console.log('获取小区列表',object);
					this.communityOptions = object.map(v=>{
						return {
							value: v.communityId,
							text: v.communityName,
						}
					})
				})
			},
			//选择小区
			fun_communityOptionsChange(e) {
				console.log('communityOptionsChange', e);
				this.formData.communityId = e.value;
				this.formData.communityName = e.text;

				this.formData.buildingId = '';
				this.formData.buildingName = '';
				this.formData.unitId = '';
				this.formData.unitName = '';
				this.formData.householdId = '';
				this.formData.householdName = '';
				this.formData.deviceId = '';
				this.formData.deviceName = '';
				if(this.formData.deviceTypeId == 8){
					this.fun_getDeviceOptions();//获取设备列表
				}else{
					this.fun_getBuildingOptions();//获取楼栋列表
				}
			},
			//获取楼栋列表
			fun_getBuildingOptions() {
				this.http.post(this.$apiUrl.getBuildingOptions,{
					data:{
						communityId: this.formData.communityId,
					}
				}).then( ({object}) => {
					console.log('获取楼栋列表',object);
					this.buildingOptions = object.map(v=>{
						return {
							value: v.buildingId,
							text: v.buildingName,
						}
					})
				})
			},
			//选择楼栋
			fun_buildingOptionsChange(e) {
				console.log('buildingOptionsChange', e);
				this.formData.buildingId = e.value;
				this.formData.buildingName = e.text;

				this.formData.unitId = '';
				this.formData.unitName = '';
				this.formData.householdId = '';
				this.formData.householdName = '';
				this.formData.deviceId = '';
				this.formData.deviceName = '';
				this.fun_getUnitOptions();//获取单元列表
			},
			//获取单元列表
			fun_getUnitOptions() {
				this.http.post(this.$apiUrl.getUnitOptions,{
					data:{
						communityId: this.formData.communityId,
						buildingId: this.formData.buildingId,
					}
				}).then( ({object}) => {
					console.log('获取单元列表',object);
					this.unitOptions = object.map(v=>{
						return {
							value: v.buildingUnitId,
							text: v.buildingUnitName,
						}
					})
				})
			},
			//选择单元
			fun_unitOptionsChange(e) {
				console.log('unitOptionsChange', e);
				this.formData.unitId = e.value;
				this.formData.unitName = e.text;

				this.formData.householdId = '';
				this.formData.householdName = '';
				this.formData.deviceId = '';
				this.formData.deviceName = '';
				if(this.formData.deviceTypeId == 1||this.formData.deviceTypeId == 3||this.formData.deviceTypeId == 7){
					this.fun_getDeviceOptions();//获取设备列表
				}else{
					this.fun_getHouseholdOptions();//获取户列表
				}
			},
			//获取户列表
			fun_getHouseholdOptions() {
				this.http.post(this.$apiUrl.getHouseholdOptions,{
					data:{
						communityId: this.formData.communityId,
						buildingId: this.formData.buildingId,
						buildingUnitId: this.formData.unitId,
					}
				}).then( ({object}) => {
					console.log('获取户列表',object);
					this.householdOptions = object.map(v=>{
						return {
							value: v.heatUserId,
							text: v.heatUserName,
						}
					})
				})
			},
			//选择户
			fun_householdOptionsChange(e) {
				console.log('householdOptionsChange', e);
				this.formData.householdId = e.value;
				this.formData.householdName = e.text;
				this.formData.deviceId = '';
				this.formData.deviceName = '';
				this.fun_getDeviceOptions();//获取设备列表
			},
			//获取设备列表
			fun_getDeviceOptions() {
				this.http.post(this.$apiUrl.getDeviceOptions,{
					data:{
						pageNum: 1,
						pageSize: 1000,
						deviceTypeId: this.formData.deviceTypeId,
						communityId: this.formData.communityId,
						buildingId: this.formData.buildingId,
						buildingUnitId: this.formData.unitId,
						heatUserId: this.formData.householdId,
					}
				}).then( ({object}) => {
					console.log('获取设备列表',object);
					this.deviceOptions = object?.records?.map(v=>{
						return {
							value: v.id,
							text: v.equipmentName,
						}
					})
				})
			},
			//选择设备
			fun_deviceOptionsChange(e) {
				console.log('deviceOptionsChange', e);
				this.formData.deviceId = e.value;
				this.formData.deviceName = e.text;
			},

			//选择隐患类型
			fun_dangerTypeOptionsChange(e) {
				console.log('dangerTypeOptionsChange', e);
				this.formData.dangerTypeId = e.value;
				this.formData.dangerTypeName = e.text;
			},
			//选择隐患等级
			fun_dangerLevelOptionsChange(e) {
				console.log('dangerLevelOptionsChange', e);
				this.formData.dangerLevelId = e.value;
				this.formData.dangerLevelName = e.text;
			},
			//图片上传或删除成功
			fun_imageUploadChanged(e) {
				console.log('图片上传或删除成功',e);
				this.formData.fileBeforeList = e;
			},
			//图片上传或删除成功
			fun_imageUploadChanged2(e) {
				console.log('图片上传或删除成功2',e);
				this.formData.fileEndList = e;
			},
			
			//提交
			submit() {
				let formRules = [];
				if(this.formData.facilitiesId == '0'){
					formRules.push(...this.rules,...this.rules_heatStation);
				}else if(this.formData.facilitiesId == '1'){
					formRules.push(...this.rules,...this.rules_device);
					if(this.formData.deviceTypeId == 8){
						formRules.push(...this.rules_community);
					}else if(this.formData.deviceTypeId == 1||this.formData.deviceTypeId == 3||this.formData.deviceTypeId == 7){
						formRules.push(...this.rules_unit);
					}else{
						formRules.push(...this.rules_household);
					}

				}else{
					formRules.push(...this.rules);
				}
				//如果不是隐患上报
				if(!this.isFromDanger&&this.inspectTaskDialogType!='danger'){
					formRules.push(...this.rules_maintFileEndList);
				}
				this.$refs.form.validate(this.formData, formRules, true).then(res => {
					if (res.isPass) {
						console.log(this.formData)
						let params={
							deviceFacilities: this.formData.facilitiesId,

							maintenanceName: this.formData.danger,
							maintenanceType: this.formData.dangerTypeId,
							level: this.formData.dangerLevelId,
							descMsg: this.formData.desc,
							fileBeforeList: this.formData.fileBeforeList,
							fileEndList: this.formData.fileEndList,
						}
						if(this.formData.facilitiesId == '0'){
							//热力站
							params = {
								...params,
								substationId: this.formData.heatStationId,
								substationName: this.formData.heatStationName,
								equipmentUnitId: this.formData.heatStationUnitId,
								deviceTypeId: this.formData.heatStationDeviceTypeId,
								deviceId: this.formData.heatStationDeviceId,
								deviceName: this.formData.heatStationDeviceName,
							}

						}else if(this.formData.facilitiesId == '1'){
							//设备
							params = {
								...params,
								deviceTypeId: this.formData.deviceTypeId,
								communityId: this.formData.communityId,
								buildingId: this.formData.buildingId,
								buildingUnitId: this.formData.unitId,
								heatUserId: this.formData.householdId,
								deviceId: this.formData.deviceId,
								deviceName: this.formData.deviceName,
							}
						}

						if(!this.isFromDanger&&this.inspectTaskDialogType!='danger'){
							//巡检详情跳转过来的-维修上报
							if(this.inspectTaskDialogType=='maint'){
								params.planId = this.formData.planId;
								params.remark = "维修";
							}
							//新增维修或维修上报接口
							this.http.post(this.$apiUrl.maintAdd, {
								data:{
									...params,
								}
							}).then(res => {
								console.log(res);
								this.tui.toast("提交成功")
								setTimeout(() => {
									uni.navigateBack()
								}, 2000);
							})
						}else{
							//巡检详情跳转过来的-隐患上报
							params.planId = this.formData.planId;
							params.remark = "隐患";
							this.http.post(this.$apiUrl.maintPitfallAdd, {
								data:{
									...params,
								}
							}).then(res => {
								console.log(res);
								this.tui.toast("提交成功")
								setTimeout(() => {
									uni.navigateBack()
								}, 2000);
							})
						}
					} else {
						console.log('校验不通过:',res)
					}
				}).catch(errors => {
					console.log('校验失败:',errors)
				})
			},
		},
	};
</script>

<style lang="scss">
    // page{background-color: #fff;}
</style>