<template>
	<view class="container">
		<!-- 列表菜单 -->
        <view class="font-14">
			<view class="bg-color-fff marg-10 padd-10 border-radius-5" v-if="!dataInfo.riskId">
				<view class="border-b-ddd padd-5_0 flex flex-both">
					<text class="font-bold font-16">基本信息</text>
					<tui-tag :type="maintTagType[dataInfo.warningLevel+2]" padding="6rpx 12rpx">{{alarmLevelOptions.find(v=>v.value==dataInfo.warningLevel)?.text}}</tui-tag>
				</view>
				<view class="padd-t-5">
					<view>归属模块：{{relationTypeOptions.find(v=>v.value==dataInfo.relationType)?.text}}</view>
					<view>归属编号：{{dataInfo.relationNo}}</view>
					<view>归属名称：{{dataInfo.relationName}}</view>
					<view>点位编号：{{dataInfo.heatPositionNo}}</view>
					<view class="flex">
						<text class="flex-item-shrink-0">点位名称：</text>
						<text>{{dataInfo.heatPositionName}}</text>
					</view>
					<view>报警内容：{{dataInfo.warningContent}}</view>
					<!-- <view>报警值：{{dataInfo.warningValue}}</view> -->
					<!-- <view>报警点位置：{{'??'}}</view> -->
					<view>报警时间：{{dataInfo.warningTime}}</view>
					<view>确认状态：{{dataInfo.confirmFlag==1?'已确认':'未确认'}}</view>
					<view>确认时间：{{dataInfo.updateTime}}</view>
					<view>确认人：{{dataInfo.updateLoginName}}</view>
					<view>报警状态：{{dataInfo.restoreTime==1?'已恢复':'报警中'}}</view>
					<view>处理状态：{{dataInfo.bjczjdmc}}</view>
				</view>
			</view>
			<!-- 事件处理 -->
			<view class="bg-color-fff marg-10 padd-10 border-radius-5 text-right" v-if="isFromAwaitHandle&&(!dataInfo.bjczjddm||dataInfo.bjczjddm==2||dataInfo.bjczjddm==5)">
				<tui-tag style="margin-left: 20rpx;" type="warning" plain v-if="!dataInfo.bjczjddm||dataInfo.bjczjddm==2" @click="fun_goPage(dataInfo,'maint')">派发维修</tui-tag>
				<tui-tag style="margin-left: 20rpx;" type="green" plain v-else-if="dataInfo.bjczjddm==5" @click="fun_goPage(dataInfo,'end')">办结</tui-tag>
				<tui-tag style="margin-left: 20rpx;" type="primary" plain v-if="dataInfo.confirmFlag == '0'" @click="fun_confirm(dataInfo.heatWarningRecordId)">确认</tui-tag>
			</view>
			<!-- 办理过程 -->
			<view class="bg-color-fff marg-10 padd-10 border-radius-5">
				<view class="border-b-ddd padd-5_0 flex flex-both">
					<text class="font-bold font-16">处理过程</text>
				</view>
				<view class="padd-t-5 padd-l-15 posit-relat">
					<tui-time-axis v-if="handleProcessList.length>0">
						<tui-timeaxis-item v-for="(item, index) in handleProcessList" :key="index" backgroundColor="transparent">
							<template v-slot:node>
								<view class="tui-node bg-color-blue">
									<tui-icon name="check" color="#fff" :size="14" bold></tui-icon>
								</view>
							</template>

							<template v-slot:content>
								<view class="line-1s6">
									<view class="font-bold">{{ item.maintenanceStep }}</view>
									<view class="">时间：{{ item.createTime }}</view>
									<view class="">处理人：{{ item.createUserName }}</view>
									<view class="" v-if="item.remark">处置状态：{{ item.remark }}</view>
									<view class="" v-if="item.handleResult">研判结果：{{ item.handleResult }}</view>
									<view class="bg-color-ddd padd-5 flex">
										<text class="flex-item-shrink-0">意见：</text>
										<text>{{ item.maintenanceMsg }}</text>
									</view>
									<view class="grid grid-temp-col-12 grid-items-center-center" v-if="item.imagesList&&item.imagesList.length > 0">
										<view class="padd-5 grid-span-4" v-for="(img,key) of item.imagesList" :key="key">
											<image class="" style="width:180rpx;height:180rpx;" :src="img" @click="tui.fun_bigImg(img,item.imagesList)"></image>
										</view>
									</view>
								</view>
							</template>
						</tui-timeaxis-item>
					</tui-time-axis>
					<view class="no-data" v-else>暂无办理记录</view>
				</view>
			</view>
        </view>
	</view>
</template>

<script>
	import { mapActions, mapState } from 'vuex';
	import fileUpload from '@/pages/component/file-upload.vue'

	export default {
		components: {
			fileUpload,
		},
		data() {
			return {
				isAdmin: this.tui.getUserInfo('isAdmin'),
				dataInfo: {},
				//归属模块
				relationTypeOptions: [
					{text:'热力站',value:'0'},
					{text:'热源',value:'1'},
					{text:'环保设备',value:'2'},
					{text:'机组',value:'3'},
				],
				alarmLevelOptions: [],//报警等级
				dangerTypeOptions: [],//隐患类型
				dangerStatusOptions: [],//隐患状态
				maintTagType: {
					1: 'light-brownish',
					2: 'light-orange',
					3: 'warning',
					4: 'danger',
				},
				// bjztOption: [],//报警状态
				riskTagType: {
					0: 'warning',
					1: 'warning',
					2: 'primary',
					3: 'danger',
					4: 'green',
				},
				// riskTagType: {
				// 	1: 'light-brownish',
				// 	2: 'light-brownish',
				// 	3: 'light-brownish',
				// 	4: 'light-orange',
				// 	5: 'light-blue',
				// 	6: 'light-green',
				// 	7: 'light-danger',
				// 	8: 'light-danger',
				// 	9: 'light-green',
				// },
				formData: {
					maintStateName: '',
					maintStateId: '',
					maintenanceMsg: '',
					fileList: [],
				},
				rules: [
					{name: "maintStateId",rule: ["required"],msg: ["请选择"]},
					{name: "maintenanceMsg",rule: ["required"],msg: ["请输入描述"]},
					{name: "fileList",rule: ["required"],msg: ["请上传图片"],
						validator: [{msg: "请上传图片",method: (value) => {
							return value.length > 0;
						}}]
					},
				],
				maintStateOptionsShow: false,
				maintStateOptions: [
					{text: '维修完成',value: 1},
					{text: '无需维修',value: 2},
				],
				//处理记录
				handleProcessList: [
				// 	{
				// 		createTime: '2018-04-15 10:10:10',
				// 		typeValue: '1',
				// 		typeName: '报警研判',
				// 		clr: '处理人',
				// 		remark: '设备故障，需要处理',
				// 		handleResult: '报警处置',
				// 		imagesList:[
				// 			'/static/images/index/head_bg.jpg',
				// 			'/static/images/my/userphoto.png',
				// 			'/static/images/my/head-bg.jpg',
				// 		],
				// 	},
				// 	{
				// 		createTime: '2018-04-13',
				// 		typeValue: '2',
				// 		typeName: '故障维修',
				// 		clr: '处理人',
				// 		remark: '备注',
				// 	},
				// 	{
				// 		createTime: '2018-04-11',
				// 		typeValue: '3',
				// 		typeName: '处置办结',
				// 		clr: '处理人',
				// 		remark: '备注',
				// 	},
				],

				//待办报警页面跳转过来的
				isFromAwaitHandle: false,
			};
		},
		computed: mapState(['isOnline']),
		onLoad(e) {
			console.log('onLoad',e);
			if(e.gid){
				this.formData.gid = e.gid;
				this.fun_getDicts();//获取字典表数据
				// this.fun_getDetail();//获取详情
			}
			//待办报警页面跳转过来的
			if(e.isFromAwaitHandle){
				this.isFromAwaitHandle = e.isFromAwaitHandle;
			}
		},
		onShow() {
			if(this.formData.gid){
				this.fun_getDetail();//获取详情
			}
		},
        //下拉刷新
        onPullDownRefresh() {
        	setTimeout(function () {
        		uni.stopPullDownRefresh();
        	}, 200);
        	// this.fun_getTotal();//
        },
		methods: {
			...mapActions(['getOnlineStatus']),
			//获取字典表数据
			fun_getDicts() {
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHLX"}
				// }).then( ({data}) => {
				// 	console.log('隐患类型:',data)
				// 	this.dangerTypeOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getDict, {
				// 	data:{type:"YHDJ"}
				// }).then( ({data}) => {
				// 	console.log('报警等级:',data)
				// 	this.alarmLevelOptions = data.list;
				// })
				// this.http.get(this.$apiUrl.getBusinessDict+'BJZT').then( ({data}) => {
				// 	console.log('报警状态:',data)
				// 	this.bjztOption = data;
				// })
				this.alarmLevelOptions = this.$apiUrl.localCommonDict.alarmLevelOptions;
				// this.dangerStatusOptions = this.$apiUrl.localCommonDict.dangerStatusOptions;
			},
			//获取详情
			fun_getDetail() {
				this.http.get(this.$apiUrl.alarmDetail, {
					data: {
						keyId: this.formData.gid,
					}
				}).then( ({object}) => {
					console.log('获取详情',object);
					if(!object){
						this.tui.toast('数据不存在');
						return;
					}
					this.dataInfo = object;
					this.handleProcessList=object.stepList||[];
					if(object.stepList&&object.stepList.length > 0) {
						object.stepList.forEach(item => {
							item.imagesList = item.fileList.map(v=>v.fileUrl);
						})
					}
				})
			},
			//维修表单
			//显示维修状态
			fun_maintStateOptionsShow(e) {
				this.maintStateOptionsShow = true;
			},
			//隐藏维修状态
			fun_maintStateOptionsHide(e) {
				this.maintStateOptionsShow = false;
			},
			//选择维修状态
			fun_maintStateOptionsChange(e) {
				console.log('maintStateOptionsChange', e);
				this.formData.maintStateName = e.text;
				this.formData.maintStateId = e.value;
			},
			//图片上传或删除成功
			fun_imageUploadChanged(e) {
				console.log('fun_imageUploadChanged', e);
				this.formData.fileList = e;
			},
			//跳转
			fun_goPage(item,type) {
				uni.navigateTo({
					url: `/pages/event-handle/event-handle?gid=${item.heatWarningRecordId}&type=${type}&deviceId=${item.heatPositionId}&deviceName=${item.heatPositionName}&from=alarm`
				});
			},
			//确认
			fun_confirm(id) {
				this.tui.modal('确认', '是否确认？', true, (res) => {
					if (res) {
						this.http.get(this.$apiUrl.alarmConfirm, { data:{keyIds:id.toString(),action:'确认'} }).then( (res) => {
							this.tui.toast('确认成功');
							setTimeout(()=>{
								uni.navigateBack();
							},1500)
						})
					}
				})
			}
		},
	};
</script>

<style lang="scss">
    // page{background-color: #fff;}
	.tui-node {
		height: 44rpx;
		width: 44rpx;
		border-radius: 50%;
		background-color: #ddd;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		flex-shrink: 0;
	}
</style>