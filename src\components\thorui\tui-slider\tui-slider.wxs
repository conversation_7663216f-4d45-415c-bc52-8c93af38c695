var sliderObj = {
	width: 280,
	blockWidth: 20,
	step: 1,
	min: 0,
	max: 100,
	disabled: false,
	section: false,
	start: 0,
	end: 0,
	drag: false
}

function bool(str) {
	return str === 'true' || str == true ? true : false
}

function format(value) {
	return Math.round(Math.max(sliderObj.min, Math.min(value, sliderObj.max)) / sliderObj.step) * sliderObj.step;
}
function isPC() {
	if (typeof navigator !== 'object') return false;
	var userAgentInfo = navigator.userAgent;
	var Agents = ["Android", "iPhone", "SymbianOS", "Windows Phone", "iPad", "iPod"];
	var flag = true;
	for (var v = 0; v < Agents.length - 1; v++) {
		if (userAgentInfo.indexOf(Agents[v]) > 0) {
			flag = false;
			break;
		}
	}
	return flag;
}
var isH5 = false
if (typeof window === 'object') isH5 = true


function setInitValue(dataset, isChange) {
	sliderObj.drag = false
	sliderObj.width = +dataset.width
	sliderObj.blockWidth = +dataset.blockwidth
	sliderObj.step = +dataset.step
	sliderObj.min = +dataset.min || 0
	sliderObj.max = +dataset.max
	sliderObj.disabled = bool(dataset.disabled)
	sliderObj.section = bool(dataset.section)
	sliderObj.start = format(+dataset.value) || 0
	sliderObj.end = format(+dataset.endvalue)
}

function touchstart(e, ins) {
	var state = e.instance.getState()
	var touch = e.touches[0] || e.changedTouches[0]
	if (isH5 && isPC()) {
		touch = e;
	}
	var dataset = e.instance.getDataset()
	state.startX = touch.pageX
	setInitValue(dataset)
}

function sectionTouchstart(e, ins) {
	var state = e.instance.getState()
	var touch = e.touches[0] || e.changedTouches[0]
	if (isH5 && isPC()) {
		touch = e;
	}
	var dataset = e.instance.getDataset()
	state.sectionStartX = touch.pageX
	setInitValue(dataset)
}

function changeValue(value, isStart, isEnd, ins) {
	var params = {
		value: value,
		isStart: isStart
	}
	if (isEnd) {
		ins.callMethod('change', params)
	} else {
		ins.callMethod('changing', params)
	}
}

function styleChange(value, ins, isEnd, state) {
	if (!ins) return;
	value = format(value);
	if (sliderObj.section) {
		value = value > sliderObj.end ? sliderObj.end : value;
	}
	changeValue(value, true, isEnd, ins)
	var dvalue = sliderObj.max - sliderObj.min;
	var maxWidth = sliderObj.width - sliderObj.blockWidth;
	var width = (value - sliderObj.min) / dvalue * maxWidth;
	var block = ins.selectComponent('.tui-slider__block');
	var glided = ins.selectComponent('.tui-slider__glided');
	if (!block || !glided) return;
	if (state) {
		state.lastLeft = width
	}
	block.setStyle({
		transform: 'translate3d(' + width + 'px,0,0)',
		'z-index': value === sliderObj.max ? 3 : 2
	})
	glided.setStyle({
		width: width + 'px'
	})
}

function styleSectionChange(value, ins, isEnd, state) {
	if (!ins) return;
	value = format(value);
	var total = sliderObj.max + sliderObj.min;
	var val = total - value;
	val = val < sliderObj.start ? sliderObj.start : val;
	value = total - val;
	changeValue(val, false, isEnd, ins)
	var dvalue = sliderObj.max - sliderObj.min;
	var maxWidth = sliderObj.width - sliderObj.blockWidth;
	var width = (value - sliderObj.min) / dvalue * maxWidth;
	var block = ins.selectComponent('.tui-section__block');
	var glided = ins.selectComponent('.tui-section__glided');
	if (state) {
		state.lastSectionLeft = width
	}
	if(!block || !glided) return;
	block.setStyle({
		transform: 'translate3d(-' + width + 'px,0,0)'
	})
	glided.setStyle({
		width: width + 'px'
	})
}

function touchmove(e, ins, event) {
	if (sliderObj.disabled) return;
	if (e.preventDefault) {
		// 阻止页面滚动
		e.preventDefault()
	}
	var state = {}
	var touch = {}
	if (isH5 && isPC()) {
		touch = e;
		state = event.instance.getState()
	} else {
		touch = e.touches[0] || e.changedTouches[0]
		state = e.instance.getState()
	}
	var pageX = touch.pageX;
	sliderObj.drag = true
	var left = pageX - state.startX + (state.lastLeft || 0);
	left = left < 0 ? 0 : left;
	var maxWidth = sliderObj.width - sliderObj.blockWidth;
	left = left >= maxWidth ? maxWidth : left;
	var dvalue = sliderObj.max - sliderObj.min;
	var value = (left / maxWidth) * dvalue + sliderObj.min;
	state.startX = pageX
	state.lastLeft = left
	styleChange(value, ins, false)
}

function sectionTouchmove(e, ins, event) {
	if (sliderObj.disabled) return;
	if (e.preventDefault) {
		// 阻止页面滚动
		e.preventDefault()
	}
	var state = {}
	var touch = {}
	if (isH5 && isPC()) {
		touch = e;
		state = event.instance.getState()
	} else {
		touch = e.touches[0] || e.changedTouches[0]
		state = e.instance.getState()
	}
	var pageX = touch.pageX;
	sliderObj.drag = true
	var left = state.sectionStartX - pageX + (state.lastSectionLeft || 0);
	left = left < 0 ? 0 : left;
	var maxWidth = sliderObj.width - sliderObj.blockWidth;
	left = left >= maxWidth ? maxWidth : left;
	var dvalue = sliderObj.max - sliderObj.min;
	var value = (left / maxWidth) * dvalue + sliderObj.min;
	state.sectionStartX = pageX
	state.lastSectionLeft = left
	styleSectionChange(value, ins, false)
}

function touchend(e, ins, event) {
	if (sliderObj.disabled) return;
	if (sliderObj.drag) {
		var state = {}
		if (isH5 && isPC()) {
			state = event.instance.getState()
		} else {
			state = e.instance.getState()
		}
		var maxWidth = sliderObj.width - sliderObj.blockWidth;
		var dvalue = sliderObj.max - sliderObj.min;
		var value = (state.lastLeft / maxWidth) * dvalue + sliderObj.min;
		styleChange(value, ins, true, state)
	}
}



function sectionTouchend(e, ins, event) {
	if (sliderObj.disabled) return;
	if (sliderObj.drag) {
		var state = {}
		if (isH5 && isPC()) {
			state = event.instance.getState()
		} else {
			state = e.instance.getState()
		}
		var maxWidth = sliderObj.width - sliderObj.blockWidth;
		var dvalue = sliderObj.max - sliderObj.min;
		var value = (state.lastSectionLeft / maxWidth) * dvalue + sliderObj.min;
		styleSectionChange(value, ins, true, state)
	}
}

function slidevalue(value, oldValue, owner, ins) {
	var state = ins.getState()
	state.startX = 0;
	state.lastLeft = 0;
	var dataset = ins.getDataset()
	setInitValue(dataset)
	styleChange(value, owner, true, state)
}


function sectionSlidevalue(value, oldValue, owner, ins) {
	var state = ins.getState()
	state.sectionStartX = 0;
	state.lastSectionLeft = 0;
	var dataset = ins.getDataset()
	setInitValue(dataset)
	value = sliderObj.max - value + sliderObj.min
	styleSectionChange(value, owner, true, state)
}

var movable = false;

function mousedown(e, ins) {
	if (!isH5 || !isPC()) return
	touchstart(e, ins)
	movable = true
	window.onmousemove = function(event) {
		if (!isH5 || !isPC() || !movable) return
		touchmove(event, ins, e)
	}
	window.onmouseup = function(event) {
		if (!isH5 || !isPC() || !movable) return
		touchend(event, ins, e)
		movable = false
	}
}

var endMovable = false;

function endMousedown(e, ins) {
	if (!isH5 || !isPC()) return
	sectionTouchstart(e, ins)
	endMovable = true
	window.onmousemove = function(event) {
		if (!isH5 || !isPC() || !endMovable) return
		sectionTouchmove(event, ins, e)
	}
	window.onmouseup = function(event) {
		if (!isH5 || !isPC()) return
		sectionTouchend(event, ins, e)
		endMovable = false
	}
}

module.exports = {
	touchstart: touchstart,
	touchmove: touchmove,
	touchend: touchend,
	mousedown: mousedown,
	slidevalue: slidevalue,
	sectionTouchstart: sectionTouchstart,
	sectionTouchmove: sectionTouchmove,
	sectionTouchend: sectionTouchend,
	endMousedown: endMousedown,
	sectionSlidevalue: sectionSlidevalue
}
